# 🚂 Railway部署指南 - Win222 Brasil

本指南将帮助您将Win222 Brasil项目部署到Railway平台。

## 📋 部署前准备

### 1. 确保项目已推送到GitHub
```bash
git add .
git commit -m "feat: Add Railway deployment configuration"
git push origin main
```

### 2. 准备环境变量
确保您有以下环境变量的值：
- Google Analytics ID
- Facebook Pixel ID (可选)
- 其他API密钥

## 🚀 Railway部署步骤

### 步骤1: 连接GitHub仓库
1. 访问 [Railway.app](https://railway.app)
2. 使用GitHub账号登录
3. 点击 "New Project"
4. 选择 "Deploy from GitHub repo"
5. 选择您的 `win222` 仓库

### 步骤2: 配置项目设置
1. Railway会自动检测到这是一个Next.js项目
2. 确认构建命令: `npm run build`
3. 确认启动命令: `npm start`
4. 端口会自动设置为 `$PORT` 环境变量

### 步骤3: 设置环境变量
在Railway项目的Variables标签页中添加以下环境变量：

#### 必需的环境变量:
```env
NODE_ENV=production
NEXT_PUBLIC_SITE_URL=https://your-app-name.railway.app
NEXT_PUBLIC_SITE_NAME=Win222 Brasil
```

#### SEO相关 (推荐):
```env
NEXT_PUBLIC_GOOGLE_SITE_VERIFICATION=your_verification_code
GA_MEASUREMENT_ID=G-XXXXXXXXXX
GA_API_SECRET=your_ga_api_secret
```

#### 联盟营销 (可选):
```env
FACEBOOK_PIXEL_ID=your_pixel_id
FACEBOOK_ACCESS_TOKEN=your_fb_token
AFFILIATE_TRACKING_DOMAIN=your-app-name.railway.app
AFFILIATE_DEFAULT_CAMPAIGN=win222_brasil
```

### 步骤4: 部署
1. 点击 "Deploy" 按钮
2. Railway会自动构建和部署您的应用
3. 部署完成后，您会获得一个 `.railway.app` 域名

## 🔧 部署后配置

### 1. 自定义域名 (可选)
1. 在Railway项目设置中点击 "Domains"
2. 添加您的自定义域名 (如: win222brasil.com)
3. 配置DNS记录指向Railway提供的CNAME

### 2. SSL证书
- Railway自动为所有域名提供SSL证书
- 包括自定义域名的Let's Encrypt证书

### 3. 环境变量更新
部署后，更新以下环境变量为实际域名：
```env
NEXT_PUBLIC_SITE_URL=https://win222brasil.com
```

## 📊 监控和维护

### 1. 查看部署日志
- 在Railway控制台的 "Deployments" 标签页查看构建和运行日志
- 监控应用性能和错误

### 2. 自动部署
- Railway会自动监听GitHub仓库的变更
- 每次推送到main分支都会触发新的部署

### 3. 数据库 (如需要)
如果将来需要数据库：
```bash
# 在Railway项目中添加PostgreSQL
railway add postgresql

# 或添加Redis
railway add redis
```

## 🔍 故障排除

### 常见问题:

1. **构建失败**
   - 检查 `package.json` 中的scripts
   - 确保所有依赖都在 `dependencies` 中

2. **环境变量问题**
   - 确保所有必需的环境变量都已设置
   - 检查变量名称是否正确

3. **端口问题**
   - Next.js会自动使用 `process.env.PORT`
   - 无需手动配置端口

### 有用的命令:
```bash
# 本地测试生产构建
npm run build
npm start

# 检查构建输出
npm run build && ls -la .next/
```

## 📈 性能优化

### 1. 启用压缩
Railway自动启用gzip压缩

### 2. CDN配置
- Railway提供全球CDN
- 静态资源自动缓存

### 3. 监控
设置以下监控：
- Railway内置监控
- Google Analytics
- 自定义性能监控

## 🔐 安全配置

### 1. 环境变量安全
- 敏感信息只存储在Railway环境变量中
- 不要在代码中硬编码API密钥

### 2. HTTPS
- Railway强制使用HTTPS
- 自动重定向HTTP到HTTPS

## 📞 支持

如果遇到问题：
1. 查看Railway文档: https://docs.railway.app
2. 检查项目日志
3. 联系Railway支持团队

---

🎉 **恭喜！您的Win222 Brasil网站现在已在Railway上运行！**

访问您的网站: `https://your-app-name.railway.app`
