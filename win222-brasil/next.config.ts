import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  // SEO优化配置
  trailingSlash: false,

  // 性能优化
  compress: true,
  poweredByHeader: false,

  // Railway部署优化
  output: 'standalone',
  experimental: {
    optimizePackageImports: ['react-icons'],
  },

  // 图片优化
  images: {
    formats: ['image/webp', 'image/avif'],
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
    minimumCacheTTL: 60,
  },

  // 国际化配置 - 巴西葡萄牙语
  i18n: {
    locales: ['pt-BR'],
    defaultLocale: 'pt-BR',
  },

  // 安全头部
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'Referrer-Policy',
            value: 'origin-when-cross-origin',
          },
        ],
      },
    ];
  },

  // 重定向配置
  async redirects() {
    return [
      {
        source: '/home',
        destination: '/',
        permanent: true,
      },
    ];
  },
};

export default nextConfig;
