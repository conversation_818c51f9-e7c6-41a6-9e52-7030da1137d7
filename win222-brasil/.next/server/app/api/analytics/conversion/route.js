"use strict";(()=>{var e={};e.id=153,e.ids=[153],e.modules={846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1923:(e,t,r)=>{r.r(t),r.d(t,{patchFetch:()=>_,routeModule:()=>p,serverHooks:()=>v,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>u});var n={};r.r(n),r.d(n,{OPTIONS:()=>l,POST:()=>c});var o=r(6559),s=r(8088),a=r(7719),i=r(2190);async function c(e){try{let t=await e.json();if(!t.type||!t.affiliateId||!t.timestamp)return i.NextResponse.json({error:"Missing required fields"},{status:400});let r=e.headers.get("x-forwarded-for")||e.headers.get("x-real-ip")||"unknown",n=e.headers.get("x-vercel-ip-country")||e.headers.get("cf-ipcountry")||"unknown",o=e.headers.get("x-vercel-ip-city")||"unknown",s=e.headers.get("x-vercel-ip-country-region")||"unknown",a={...t,ip:r,country:n,city:o,region:s,processed_at:new Date().toISOString()};if(console.log("Conversion Event:",JSON.stringify(a,null,2)),process.env.GA_MEASUREMENT_ID&&process.env.GA_API_SECRET)try{let e=await fetch(`https://www.google-analytics.com/mp/collect?measurement_id=${process.env.GA_MEASUREMENT_ID}&api_secret=${process.env.GA_API_SECRET}`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({client_id:t.userId||"anonymous",events:[{name:"affiliate_conversion",parameters:{event_category:"affiliate",event_label:t.affiliateId,value:t.value||0,conversion_type:t.type,affiliate_id:t.affiliateId,custom_parameters:t.metadata}}]})});e.ok||console.error("Failed to send to Google Analytics:",await e.text())}catch(e){console.error("Error sending to Google Analytics:",e)}if(process.env.FACEBOOK_PIXEL_ID&&process.env.FACEBOOK_ACCESS_TOKEN)try{let e=await fetch(`https://graph.facebook.com/v18.0/${process.env.FACEBOOK_PIXEL_ID}/events`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({data:[{event_name:"Purchase",event_time:Math.floor(new Date(t.timestamp).getTime()/1e3),action_source:"website",user_data:{client_ip_address:r,client_user_agent:t.userAgent},custom_data:{currency:"BRL",value:t.value||0,content_type:"affiliate_conversion",content_category:t.affiliateId}}],access_token:process.env.FACEBOOK_ACCESS_TOKEN})});e.ok||console.error("Failed to send to Facebook Pixel:",await e.text())}catch(e){console.error("Error sending to Facebook Pixel:",e)}return i.NextResponse.json({success:!0,message:"Conversion tracked successfully",event_id:`${t.affiliateId}_${Date.now()}`})}catch(e){return console.error("Error processing conversion:",e),i.NextResponse.json({error:"Internal server error"},{status:500})}}async function l(){return new i.NextResponse(null,{status:200,headers:{"Access-Control-Allow-Origin":"*","Access-Control-Allow-Methods":"POST, OPTIONS","Access-Control-Allow-Headers":"Content-Type"}})}let p=new o.AppRouteRouteModule({definition:{kind:s.RouteKind.APP_ROUTE,page:"/api/analytics/conversion/route",pathname:"/api/analytics/conversion",filename:"route",bundlePath:"app/api/analytics/conversion/route"},resolvedPagePath:"/home/<USER>/文档/win222/win222-brasil/src/app/api/analytics/conversion/route.ts",nextConfigOutput:"standalone",userland:n}),{workAsyncStorage:d,workUnitAsyncStorage:u,serverHooks:v}=p;function _(){return(0,a.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:u})}},3033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},9294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[76],()=>r(1923));module.exports=n})();