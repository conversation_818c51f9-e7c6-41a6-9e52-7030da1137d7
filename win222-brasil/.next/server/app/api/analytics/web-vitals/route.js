"use strict";(()=>{var e={};e.id=11,e.ids=[11],e.modules={846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},3033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},7431:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>v,routeModule:()=>d,serverHooks:()=>g,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>u});var s={};t.r(s),t.d(s,{GET:()=>c,POST:()=>p});var o=t(6559),n=t(8088),a=t(7719),i=t(2190);async function p(e){try{let{name:r,value:t,id:s,url:o,timestamp:n}=await e.json();if(!r||!t||!s)return i.NextResponse.json({error:"Missing required fields"},{status:400});if(console.log("Web Vitals Metric:",{name:r,value:t,id:s,url:o,timestamp:n,userAgent:e.headers.get("user-agent"),ip:e.headers.get("x-forwarded-for")||e.headers.get("x-real-ip")||"unknown"}),process.env.GA_MEASUREMENT_ID&&process.env.GA_API_SECRET)try{let e=await fetch(`https://www.google-analytics.com/mp/collect?measurement_id=${process.env.GA_MEASUREMENT_ID}&api_secret=${process.env.GA_API_SECRET}`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({client_id:s,events:[{name:"web_vitals",params:{metric_name:r,metric_value:t,page_location:o}}]})});e.ok||console.error("Failed to send to Google Analytics:",e.statusText)}catch(e){console.error("Error sending to Google Analytics:",e)}let a=((e,r)=>{let t={CLS:{good:.1,needsImprovement:.25},INP:{good:200,needsImprovement:500},FCP:{good:1800,needsImprovement:3e3},LCP:{good:2500,needsImprovement:4e3},TTFB:{good:800,needsImprovement:1800}}[e];return t?r<=t.good?"good":r<=t.needsImprovement?"needs-improvement":"poor":"unknown"})(r,t),p={name:r,value:t,id:s,url:o,timestamp:n,rating:a,userAgent:e.headers.get("user-agent"),country:e.headers.get("x-vercel-ip-country")||"unknown",city:e.headers.get("x-vercel-ip-city")||"unknown"};return"poor"===a&&console.warn(`Poor ${r} performance detected:`,p),i.NextResponse.json({success:!0,rating:a,message:"Web vitals metric recorded successfully"},{status:200})}catch(e){return console.error("Error processing web vitals:",e),i.NextResponse.json({error:"Internal server error"},{status:500})}}async function c(){try{return i.NextResponse.json({summary:{totalPageViews:1e4,averageLCP:2100,averageINP:150,averageCLS:.08,averageFCP:1600,averageTTFB:650},ratings:{good:75,needsImprovement:20,poor:5},topPages:[{url:"/",views:5e3,avgLCP:2e3},{url:"/jogos",views:2e3,avgLCP:2200},{url:"/cassino",views:1500,avgLCP:2100}]},{status:200})}catch(e){return console.error("Error fetching web vitals stats:",e),i.NextResponse.json({error:"Internal server error"},{status:500})}}let d=new o.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/analytics/web-vitals/route",pathname:"/api/analytics/web-vitals",filename:"route",bundlePath:"app/api/analytics/web-vitals/route"},resolvedPagePath:"/home/<USER>/文档/win222/win222-brasil/src/app/api/analytics/web-vitals/route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:l,workUnitAsyncStorage:u,serverHooks:g}=d;function v(){return(0,a.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:u})}},9294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[76],()=>t(7431));module.exports=s})();