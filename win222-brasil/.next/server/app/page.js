"use strict";(()=>{var e={};e.id=974,e.ids=[974],e.modules={755:(e,r,t)=>{t.r(r),t.d(r,{GlobalError:()=>s.a,__next_app__:()=>u,pages:()=>l,routeModule:()=>h,tree:()=>p});var n=t(5239),i=t(8088),a=t(8170),s=t.n(a),o=t(893),d={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>o[e]);t.d(r,d);let p={children:["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,1204)),"/home/<USER>/文档/win222/win222-brasil/src/app/page.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,4511)),"/home/<USER>/文档/win222/win222-brasil/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,l=["/home/<USER>/文档/win222/win222-brasil/src/app/page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},h=new n.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:p}})},846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1204:(e,r,t)=>{t.r(r),t.d(r,{default:()=>s,metadata:()=>a});var n=t(7413),i=t(6971);let a={title:i.tO.home.title,description:i.tO.home.description,keywords:i.tO.home.keywords};function s(){return(0,n.jsxs)("div",{children:[(0,n.jsx)("h1",{children:"Win222 Brasil - Teste"}),(0,n.jsx)("p",{children:"P\xe1gina tempor\xe1ria para teste de build"})]})}},3033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{e.exports=require("path")},9121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9551:e=>{e.exports=require("url")}};var r=require("../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),n=r.X(0,[76],()=>t(755));module.exports=n})();