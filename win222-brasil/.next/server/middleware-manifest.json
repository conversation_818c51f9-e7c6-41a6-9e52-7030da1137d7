{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/vendors.js", "server/src/middleware.js"], "name": "src/middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico).*))(\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "iStTD0yc35gKA0p6_ZDdL", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "8EQoY+JtNEIuhj+oZfOefz1gxs1nqkJyBc38Prv0EVU=", "__NEXT_PREVIEW_MODE_ID": "f82de50084c1a95a63553cfc7bb75187", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "911be92ae5964e0e794207eb0a9357c64f5348ae9b9366b95e45c8cc8ece9a4e", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "0475603e3b51f2e4e52ae585ad98f4a38397a4d2d90efef5574f3b26806ecf41"}}}, "functions": {}, "sortedMiddleware": ["/"]}