{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/vendors.js", "server/src/middleware.js"], "name": "src/middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico).*))(\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "Kmvc1-9Pyy1fLc8iRIuLG", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "8EQoY+JtNEIuhj+oZfOefz1gxs1nqkJyBc38Prv0EVU=", "__NEXT_PREVIEW_MODE_ID": "bcef59688c0768b9bfb4c75d4a2f4df5", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "eef8f311c735d518362b1bd5a4d489007078d08f63f035cafcfc11d9ea8d455c", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "a1d89dbc9cc56a7c9a505e0569495848407c7899039f1617faabc7bc4365ee34"}}}, "functions": {}, "sortedMiddleware": ["/"]}