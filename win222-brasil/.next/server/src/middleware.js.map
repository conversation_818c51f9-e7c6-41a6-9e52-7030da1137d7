{"version": 3, "file": "src/middleware.js", "mappings": "oFAAA,uDCAA,sLCGO,SAASA,EAAWC,CAAoB,EAC7C,IAAMC,EAAWC,EAAAA,EAAYA,CAACC,IAAI,GAC5B,UAAEC,CAAQ,CAAE,CAAGJ,EAAQK,OAAO,CAGpCJ,EAASK,OAAO,CAACC,GAAG,CAAC,kBAAmB,QACxCN,EAASK,OAAO,CAACC,GAAG,CAAC,yBAA0B,WAC/CN,EAASK,OAAO,CAACC,GAAG,CAAC,kBAAmB,4BACxCN,EAASK,OAAO,CAACC,GAAG,CAAC,mBAAoB,iBACzCN,EAASK,OAAO,CAACC,GAAG,CAAC,qBAAsB,4CAgB3CN,EAASK,OAAO,CAACC,GAAG,CAAC,0BAbT,CAaoCC,8cAG5CJ,EAASK,UAAU,CAAC,kBAEtBR,CAFyC,CAEhCK,OAAO,CAACC,GAAG,CAAC,gBAAiB,uCAC7BH,EAASK,UAAU,CAAC,YAE7BR,CAF0C,CAEjCK,OAAO,CAACC,GAAG,CAAC,gBAAiB,2BAC7BH,EAASK,UAAU,CAAC,SAE7BR,CAFuC,CAE9BK,OAAO,CAACC,GAAG,CAAC,gBAAiB,uCAC7BH,oBAA4C,eAAe,CAA5BA,EAExCH,EAASK,OAAO,CAACC,GAAG,CAAC,gBAAiB,yBAChB,kBAAkB,CAA/BH,EAETH,EAASK,OAAO,CAACC,GAAG,CAAC,gBAAiB,0BAGtCN,EAASK,OAAO,CAACC,GAAG,CAAC,gBAAiB,sDAIP,UAAU,CAAvCP,EAAQK,OAAO,CAACK,QAAQ,EAC1BT,EAASK,OAAO,CAACC,GAAG,CAAC,4BAA6B,gDAIpDN,EAASK,OAAO,CAACC,GAAG,CAAC,OAAQ,mBAGZ,KAAK,CAAlBH,GAEFH,EAASK,OAAO,CAACC,GAAG,CAAC,OAAQ,yLAQ3BH,EAASK,UAAU,CAAC,WAAaL,EAASK,UAAU,CAAC,cAAa,EAC3DH,OAAO,CAACC,GAAG,CAAC,OAAQ,8EAO/B,IAAMI,EAAOX,EAAQM,OAAO,CAACM,GAAG,CAAC,QAC3BF,EAAWV,EAAQK,OAAO,CAACK,QAAQ,CAGzC,GAAIC,GAAMF,WAAW,QAAS,CAC5B,IAAMI,EAAUF,EAAKG,OAAO,CAAC,OAAQ,IACrC,OAAOZ,EAAAA,EAAYA,CAACa,QAAQ,CAAC,GAAGL,EAAS,EAAE,EAAEG,EAAAA,EAAUT,EAAAA,EAAWJ,EAAQK,OAAO,CAACW,MAAM,EAAE,CAAE,IAC9F,CAGA,GAAiB,UAAbN,CAAwBO,CAC1B,OAAOf,EAAAA,EAAYA,CAACa,IAD+B,IACvB,CAAC,CAAC,QADmC,EACzBJ,EAAAA,EAAOP,EAAAA,EAAWJ,EAAQK,OAAO,CAACW,MAAM,EAAE,CAAE,KAItF,GAAiB,MAAbZ,GAAoBA,EAASc,QAAQ,CAAC,KACxC,CAD8C,MACvChB,EAAAA,EAAYA,CAACa,QAAQ,CAAC,GAAGL,EAAS,EAAE,EAAEC,EAAAA,EAAOP,EAASe,KAAK,CAAC,EAAG,CAAC,KAAKnB,EAAQK,OAAO,CAACW,MAAM,EAAE,CAAE,KAIxG,IAAMI,EAAYpB,EAAQM,OAAO,CAACM,GAAG,CAAC,eAAiB,GAGvD,GAFc,CAEVS,OAAO,sBAFiCC,IAAI,CAACF,KAI/CnB,EAASK,OAAO,CAACC,GAAG,CAAC,eAAgB,gFAGf,CACpB,YACA,UACA,aACA,SACA,UACD,CAEiBgB,IAAI,CAACC,GAAOJ,EAAUK,QAAQ,CAACD,KAC/C,EADsD,KAC/C,IAAItB,EAAAA,EAAYA,CAAC,YAAa,CAAEwB,OAAQ,GAAI,GAMvD,IAAMC,EADiB3B,EACQ4B,GAAG,EAAED,QAChCA,GAAuB,MAAM,CAAlBA,GACb1B,EAASK,OAAO,CAACC,GAAG,CAAC,gBAAiBoB,GAKxC,IAAME,EAAgB7B,EAAQ8B,OAAO,CAAClB,GAAG,CAAC,oBAAoBmB,OAAS,IAMvE,OALA9B,EAASK,OAAO,CAACC,GAAG,CAAC,oBAAqBsB,GAG1C5B,EAASK,OAAO,CAACC,GAAG,CAAC,kBAAmByB,KAAKC,GAAG,GAAGC,QAAQ,IAEpDjC,CACT,CAEO,IAAMkC,EAAS,CACpBC,QAAS,CAQP,gDACD,EACD,OC9IF,OACA,GAAO,CAAI,EAEX,0BACA,oBACA,wBACA,qDAA6D,EAAK,gFAClE,aACA,cACA,eACA,CAAK,EAkCU,cACf,MAAW,OAAO,EAClB,KACA,OACA,QAjCA,cACA,IACA,oBACA,CAAU,SASV,WACA,iBACA,qBAWA,OAVA,MAAkB,QAAiC,IACnD,OACA,gBACA,+CACA,CAAa,EACb,0BACA,wBACA,uBACA,uBACA,CAAa,EACb,CACA,CACA,CAOA,CAAK,CACL", "sources": ["webpack://_N_E/external commonjs \"node:buffer\"", "webpack://_N_E/external commonjs \"node:async_hooks\"", "webpack://_N_E/./src/middleware.ts", "webpack://_N_E/"], "sourcesContent": ["module.exports = require(\"node:buffer\");", "module.exports = require(\"node:async_hooks\");", "import { NextResponse } from 'next/server';\nimport type { NextRequest } from 'next/server';\n\nexport function middleware(request: NextRequest) {\n  const response = NextResponse.next();\n  const { pathname } = request.nextUrl;\n\n  // Security headers\n  response.headers.set('X-Frame-Options', 'DENY');\n  response.headers.set('X-Content-Type-Options', 'nosniff');\n  response.headers.set('Referrer-Policy', 'origin-when-cross-origin');\n  response.headers.set('X-XSS-Protection', '1; mode=block');\n  response.headers.set('Permissions-Policy', 'camera=(), microphone=(), geolocation=()');\n\n  // Content Security Policy\n  const csp = [\n    \"default-src 'self'\",\n    \"script-src 'self' 'unsafe-inline' 'unsafe-eval' https://www.google-analytics.com https://www.googletagmanager.com https://connect.facebook.net\",\n    \"style-src 'self' 'unsafe-inline' https://fonts.googleapis.com\",\n    \"font-src 'self' https://fonts.gstatic.com\",\n    \"img-src 'self' data: https: blob:\",\n    \"connect-src 'self' https://www.google-analytics.com https://analytics.google.com\",\n    \"frame-src 'none'\",\n    \"object-src 'none'\",\n    \"base-uri 'self'\",\n    \"form-action 'self'\"\n  ].join('; ');\n  \n  response.headers.set('Content-Security-Policy', csp);\n\n  // Cache control based on path\n  if (pathname.startsWith('/_next/static/')) {\n    // Static assets - cache for 1 year\n    response.headers.set('Cache-Control', 'public, max-age=31536000, immutable');\n  } else if (pathname.startsWith('/images/')) {\n    // Images - cache for 1 month\n    response.headers.set('Cache-Control', 'public, max-age=2592000');\n  } else if (pathname.startsWith('/api/')) {\n    // API routes - no cache by default\n    response.headers.set('Cache-Control', 'no-cache, no-store, must-revalidate');\n  } else if (pathname === '/sitemap.xml' || pathname === '/robots.txt') {\n    // SEO files - cache for 1 day\n    response.headers.set('Cache-Control', 'public, max-age=86400');\n  } else if (pathname === '/manifest.json') {\n    // PWA manifest - cache for 1 week\n    response.headers.set('Cache-Control', 'public, max-age=604800');\n  } else {\n    // HTML pages - cache for 1 hour with revalidation\n    response.headers.set('Cache-Control', 'public, max-age=3600, stale-while-revalidate=86400');\n  }\n\n  // HSTS for HTTPS\n  if (request.nextUrl.protocol === 'https:') {\n    response.headers.set('Strict-Transport-Security', 'max-age=31536000; includeSubDomains; preload');\n  }\n\n  // Compression hints\n  response.headers.set('Vary', 'Accept-Encoding');\n\n  // Performance hints\n  if (pathname === '/') {\n    // Preload critical resources for homepage\n    response.headers.set('Link', [\n      '</fonts/inter-var.woff2>; rel=preload; as=font; type=font/woff2; crossorigin=anonymous',\n      '</images/hero-bg.webp>; rel=preload; as=image',\n      '</styles/critical.css>; rel=preload; as=style'\n    ].join(', '));\n  }\n\n  // Early hints for critical resources\n  if (pathname.startsWith('/jogos') || pathname.startsWith('/cassino')) {\n    response.headers.set('Link', [\n      '</images/games-bg.webp>; rel=preload; as=image',\n      '</api/games>; rel=prefetch'\n    ].join(', '));\n  }\n\n  // Canonical URL enforcement\n  const host = request.headers.get('host');\n  const protocol = request.nextUrl.protocol;\n  \n  // Redirect www to non-www\n  if (host?.startsWith('www.')) {\n    const newHost = host.replace('www.', '');\n    return NextResponse.redirect(`${protocol}//${newHost}${pathname}${request.nextUrl.search}`, 301);\n  }\n\n  // Redirect HTTP to HTTPS in production\n  if (protocol === 'http:' && process.env.NODE_ENV === 'production') {\n    return NextResponse.redirect(`https://${host}${pathname}${request.nextUrl.search}`, 301);\n  }\n\n  // Trailing slash handling\n  if (pathname !== '/' && pathname.endsWith('/')) {\n    return NextResponse.redirect(`${protocol}//${host}${pathname.slice(0, -1)}${request.nextUrl.search}`, 301);\n  }\n\n  // Bot detection and rate limiting\n  const userAgent = request.headers.get('user-agent') || '';\n  const isBot = /bot|crawler|spider|scraper/i.test(userAgent);\n  \n  if (isBot) {\n    // Add special headers for bots\n    response.headers.set('X-Robots-Tag', 'index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1');\n    \n    // Block malicious bots\n    const maliciousBots = [\n      'AhrefsBot',\n      'MJ12bot', \n      'SemrushBot',\n      'DotBot',\n      'BLEXBot'\n    ];\n    \n    if (maliciousBots.some(bot => userAgent.includes(bot))) {\n      return new NextResponse('Forbidden', { status: 403 });\n    }\n  }\n\n  // Geo-targeting for Brazil\n  const requestWithGeo = request as NextRequest & { geo?: { country?: string } };\n  const country = requestWithGeo.geo?.country;\n  if (country && country !== 'BR') {\n    response.headers.set('X-Geo-Country', country);\n    // Could add geo-specific content or redirects here\n  }\n\n  // A/B testing headers\n  const abTestVariant = request.cookies.get('ab-test-variant')?.value || 'A';\n  response.headers.set('X-AB-Test-Variant', abTestVariant);\n\n  // Performance monitoring\n  response.headers.set('X-Response-Time', Date.now().toString());\n\n  return response;\n}\n\nexport const config = {\n  matcher: [\n    /*\n     * Match all request paths except for the ones starting with:\n     * - api (API routes)\n     * - _next/static (static files)\n     * - _next/image (image optimization files)\n     * - favicon.ico (favicon file)\n     */\n    '/((?!_next/static|_next/image|favicon.ico).*)',\n  ],\n};\n", "import \"next/dist/server/web/globals\";\nimport { adapter } from \"next/dist/server/web/adapter\";\n// Import the userland code.\nimport * as _mod from \"private-next-root-dir/src/middleware.ts\";\nimport { edgeInstrumentationOnRequestError } from \"next/dist/server/web/globals\";\nimport { isNextRouterError } from \"next/dist/client/components/is-next-router-error\";\nconst mod = {\n    ..._mod\n};\nconst handler = mod.middleware || mod.default;\nconst page = \"/src/middleware\";\nif (typeof handler !== 'function') {\n    throw Object.defineProperty(new Error(`The Middleware \"${page}\" must export a \\`middleware\\` or a \\`default\\` function`), \"__NEXT_ERROR_CODE\", {\n        value: \"E120\",\n        enumerable: false,\n        configurable: true\n    });\n}\n// Middleware will only sent out the FetchEvent to next server,\n// so load instrumentation module here and track the error inside middleware module.\nfunction errorHandledHandler(fn) {\n    return async (...args)=>{\n        try {\n            return await fn(...args);\n        } catch (err) {\n            // In development, error the navigation API usage in runtime,\n            // since it's not allowed to be used in middleware as it's outside of react component tree.\n            if (process.env.NODE_ENV !== 'production') {\n                if (isNextRouterError(err)) {\n                    err.message = `Next.js navigation API is not allowed to be used in Middleware.`;\n                    throw err;\n                }\n            }\n            const req = args[0];\n            const url = new URL(req.url);\n            const resource = url.pathname + url.search;\n            await edgeInstrumentationOnRequestError(err, {\n                path: resource,\n                method: req.method,\n                headers: Object.fromEntries(req.headers.entries())\n            }, {\n                routerKind: 'Pages Router',\n                routePath: '/middleware',\n                routeType: 'middleware',\n                revalidateReason: undefined\n            });\n            throw err;\n        }\n    };\n}\nexport default function nHandler(opts) {\n    return adapter({\n        ...opts,\n        page,\n        handler: errorHandledHandler(handler)\n    });\n}\n\n//# sourceMappingURL=middleware.js.map"], "names": ["middleware", "request", "response", "NextResponse", "next", "pathname", "nextUrl", "headers", "set", "csp", "startsWith", "protocol", "host", "get", "newHost", "replace", "redirect", "search", "process", "endsWith", "slice", "userAgent", "isBot", "test", "some", "bot", "includes", "status", "country", "geo", "abTestVariant", "cookies", "value", "Date", "now", "toString", "config", "matcher"], "sourceRoot": "", "ignoreList": []}