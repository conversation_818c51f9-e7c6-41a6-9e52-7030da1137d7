(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[550],{356:e=>{"use strict";e.exports=require("node:buffer")},521:e=>{"use strict";e.exports=require("node:async_hooks")},785:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>p});var s={};r.r(s),r.d(s,{config:()=>l,middleware:()=>n});var a=r(214),o=r(562),i=r(555);function n(e){let t=i.Rp.next(),{pathname:r}=e.nextUrl;t.headers.set("X-Frame-Options","DENY"),t.headers.set("X-Content-Type-Options","nosniff"),t.headers.set("Referrer-Policy","origin-when-cross-origin"),t.headers.set("X-XSS-Protection","1; mode=block"),t.headers.set("Permissions-Policy","camera=(), microphone=(), geolocation=()"),t.headers.set("Content-Security-Policy","default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://www.google-analytics.com https://www.googletagmanager.com https://connect.facebook.net; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https: blob:; connect-src 'self' https://www.google-analytics.com https://analytics.google.com; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'"),r.startsWith("/_next/static/")?t.headers.set("Cache-Control","public, max-age=31536000, immutable"):r.startsWith("/images/")?t.headers.set("Cache-Control","public, max-age=2592000"):r.startsWith("/api/")?t.headers.set("Cache-Control","no-cache, no-store, must-revalidate"):"/sitemap.xml"===r||"/robots.txt"===r?t.headers.set("Cache-Control","public, max-age=86400"):"/manifest.json"===r?t.headers.set("Cache-Control","public, max-age=604800"):t.headers.set("Cache-Control","public, max-age=3600, stale-while-revalidate=86400"),"https:"===e.nextUrl.protocol&&t.headers.set("Strict-Transport-Security","max-age=31536000; includeSubDomains; preload"),t.headers.set("Vary","Accept-Encoding"),"/"===r&&t.headers.set("Link","</fonts/inter-var.woff2>; rel=preload; as=font; type=font/woff2; crossorigin=anonymous, </images/hero-bg.webp>; rel=preload; as=image, </styles/critical.css>; rel=preload; as=style"),(r.startsWith("/jogos")||r.startsWith("/cassino"))&&t.headers.set("Link","</images/games-bg.webp>; rel=preload; as=image, </api/games>; rel=prefetch");let s=e.headers.get("host"),a=e.nextUrl.protocol;if(s?.startsWith("www.")){let t=s.replace("www.","");return i.Rp.redirect(`${a}//${t}${r}${e.nextUrl.search}`,301)}if("http:"===a)return i.Rp.redirect(`https://${s}${r}${e.nextUrl.search}`,301);if("/"!==r&&r.endsWith("/"))return i.Rp.redirect(`${a}//${s}${r.slice(0,-1)}${e.nextUrl.search}`,301);let o=e.headers.get("user-agent")||"";if(/bot|crawler|spider|scraper/i.test(o)&&(t.headers.set("X-Robots-Tag","index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1"),["AhrefsBot","MJ12bot","SemrushBot","DotBot","BLEXBot"].some(e=>o.includes(e))))return new i.Rp("Forbidden",{status:403});let n=e.geo?.country;n&&"BR"!==n&&t.headers.set("X-Geo-Country",n);let l=e.cookies.get("ab-test-variant")?.value||"A";return t.headers.set("X-AB-Test-Variant",l),t.headers.set("X-Response-Time",Date.now().toString()),t}let l={matcher:["/((?!_next/static|_next/image|favicon.ico).*)"]};r(938);let c={...s},d=c.middleware||c.default,h="/src/middleware";if("function"!=typeof d)throw Object.defineProperty(Error(`The Middleware "${h}" must export a \`middleware\` or a \`default\` function`),"__NEXT_ERROR_CODE",{value:"E120",enumerable:!1,configurable:!0});function p(e){return(0,o.O)({...e,page:h,handler:async(...e)=>{try{return await d(...e)}catch(o){let t=e[0],r=new URL(t.url),s=r.pathname+r.search;throw await (0,a.Ek)(o,{path:s,method:t.method,headers:Object.fromEntries(t.headers.entries())},{routerKind:"Pages Router",routePath:"/middleware",routeType:"middleware",revalidateReason:void 0}),o}}})}}},e=>{var t=t=>e(e.s=t);e.O(0,[96],()=>t(785));var r=e.O();(_ENTRIES="undefined"==typeof _ENTRIES?{}:_ENTRIES)["middleware_src/middleware"]=r}]);
//# sourceMappingURL=middleware.js.map