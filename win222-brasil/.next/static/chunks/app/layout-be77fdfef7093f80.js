(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[177],{347:()=>{},6320:(e,t,a)=>{Promise.resolve().then(a.t.bind(a,9243,23)),Promise.resolve().then(a.t.bind(a,2885,23)),Promise.resolve().then(a.t.bind(a,3629,23)),Promise.resolve().then(a.t.bind(a,347,23)),Promise.resolve().then(a.bind(a,8962))},8962:(e,t,a)=>{"use strict";a.d(t,{ABTestProvider:()=>l});var i=a(5155),r=a(2115),n=a(5752);let o=(0,r.createContext)(null),s={hero_cta_buttons:{id:"hero_cta_buttons",name:"Hero CTA Button Variants",isActive:!0,trafficAllocation:1,variants:[{id:"control",name:"Original Buttons",weight:50,config:{primaryText:"Cadastrar Gr\xe1tis",secondaryText:"Ver Jogos",primaryColor:"yellow",layout:"horizontal"}},{id:"variant_a",name:"Urgency Buttons",weight:25,config:{primaryText:"Come\xe7ar Agora - Gr\xe1tis!",secondaryText:"Explorar Jogos",primaryColor:"orange",layout:"horizontal"}},{id:"variant_b",name:"Benefit-focused Buttons",weight:25,config:{primaryText:"Ganhar R$ 5.000 Gr\xe1tis",secondaryText:"Ver Todos os B\xf4nus",primaryColor:"green",layout:"vertical"}}]},banner_timing:{id:"banner_timing",name:"Conversion Banner Timing",isActive:!0,trafficAllocation:.8,variants:[{id:"immediate",name:"Show Immediately",weight:33,config:{delay:0}},{id:"delayed_3s",name:"Show After 3 Seconds",weight:33,config:{delay:3e3}},{id:"delayed_10s",name:"Show After 10 Seconds",weight:34,config:{delay:1e4}}]},game_card_layout:{id:"game_card_layout",name:"Game Card Layout Test",isActive:!0,trafficAllocation:.5,variants:[{id:"standard",name:"Standard Layout",weight:50,config:{showRating:!0,showProvider:!0,buttonStyle:"overlay"}},{id:"minimal",name:"Minimal Layout",weight:50,config:{showRating:!1,showProvider:!1,buttonStyle:"bottom"}}]}};function l(e){let{children:t}=e,[a,l]=(0,r.useState)({}),[d,c]=(0,r.useState)(!1);(0,r.useEffect)(()=>{let e=localStorage.getItem("ab_tests"),t={};if(e)try{t=JSON.parse(e)}catch(e){console.error("Error parsing stored A/B tests:",e)}Object.values(s).forEach(e=>{if(!e.isActive||t[e.id]||Math.random()>e.trafficAllocation)return;let a=Math.random()*e.variants.reduce((e,t)=>e+t.weight,0);for(let i of e.variants)if((a-=i.weight)<=0){t[e.id]=i.id;break}}),l(t),localStorage.setItem("ab_tests",JSON.stringify(t)),c(!0),Object.entries(t).forEach(e=>{let[t,a]=e;(0,n.yM)({type:"view",affiliateId:"main_casino",metadata:{ab_test:t,variant:a,event:"test_assignment"}})})},[]);let m=e=>{if(!d)return null;let t=s[e],i=a[e];return t&&i&&t.variants.find(e=>e.id===i)||null};return(0,i.jsx)(o.Provider,{value:{getVariant:m,trackEvent:(e,t,a)=>{let i=m(e);i&&(0,n.yM)({type:"click",affiliateId:"main_casino",value:a,metadata:{ab_test:e,variant:i.id,event:t}})},isInTest:e=>!!a[e]},children:t})}}},e=>{var t=t=>e(e.s=t);e.O(0,[96,690,76,358],()=>t(6320)),_N_E=e.O()}]);