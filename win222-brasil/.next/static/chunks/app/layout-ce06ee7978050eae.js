(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[177],{347:()=>{},411:(e,n,t)=>{"use strict";t.d(n,{default:()=>o});var r=t(2115);function o(e){let{debug:n=!1}=e;return(0,r.useEffect)(()=>{(async()=>{try{let{onCLS:e,onINP:r,onFCP:o,onLCP:a,onTTFB:i}=await t.e(96).then(t.bind(t,7063)),s=e=>{n&&console.log("Web Vital:",e);let t=window;t.gtag&&t.gtag("event",e.name,{event_category:"Web Vitals",event_label:e.id,value:Math.round("CLS"===e.name?1e3*e.value:e.value),non_interaction:!0}),fetch("/api/analytics/web-vitals",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:e.name,value:e.value,id:e.id,url:window.location.href,timestamp:Date.now()})}).catch(e=>{n&&console.error("Failed to send web vitals:",e)})};e(s),r(s),o(s),a(s),i(s)}catch(e){n&&console.error("Failed to load web-vitals:",e)}})();let e=()=>{if(["/images/logo.png","/images/hero-banner.jpg","/images/og-image.jpg"].forEach(e=>{let n=document.createElement("link");n.rel="preload",n.as="image",n.href=e,document.head.appendChild(n)}),"IntersectionObserver"in window){let e=new IntersectionObserver((e,n)=>{e.forEach(e=>{if(e.isIntersecting){let t=e.target;t.dataset.src&&(t.src=t.dataset.src,t.classList.remove("lazy"),n.unobserve(t))}})});document.querySelectorAll("img[data-src]").forEach(n=>{e.observe(n)})}["https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuLyfAZ9hiA.woff2","https://fonts.gstatic.com/s/roboto/v30/KFOmCnqEu92Fr1Mu4mxKKTU1Kg.woff2"].forEach(e=>{let n=document.createElement("link");n.rel="preload",n.as="font",n.type="font/woff2",n.crossOrigin="anonymous",n.href=e,document.head.appendChild(n)});let e=!1,n=()=>{e=!1},t=()=>{e||(requestAnimationFrame(n),e=!0)};return window.addEventListener("scroll",t,{passive:!0}),()=>{window.removeEventListener("scroll",t)}};"complete"===document.readyState?e():window.addEventListener("load",e),"serviceWorker"in navigator&&navigator.serviceWorker.register("/sw.js").catch(e=>{n&&console.error("Service Worker registration failed:",e)})},[n]),null}},480:(e,n,t)=>{Promise.resolve().then(t.t.bind(t,9243,23)),Promise.resolve().then(t.t.bind(t,2885,23)),Promise.resolve().then(t.t.bind(t,3629,23)),Promise.resolve().then(t.t.bind(t,347,23)),Promise.resolve().then(t.bind(t,8962)),Promise.resolve().then(t.bind(t,8297)),Promise.resolve().then(t.bind(t,1335)),Promise.resolve().then(t.bind(t,411))},1335:(e,n,t)=>{"use strict";t.d(n,{default:()=>a});var r=t(2115),o=t(5695);function a(e){let{enableResourceHints:n=!0,enableImageOptimization:a=!0,enableFontOptimization:i=!0,enableCriticalCSS:s=!0,debug:l=!1}=e,c=(0,o.usePathname)(),d=(0,r.useRef)([]),m=(0,r.useRef)({}),g=(0,r.useCallback)(()=>{t.e(96).then(t.bind(t,7063)).then(e=>{let{onCLS:n,onINP:t,onFCP:r,onLCP:o,onTTFB:a}=e,i=e=>{var n;let t=function(e,n){let[t,r]={CLS:[.1,.25],INP:[200,500],FCP:[1800,3e3],LCP:[2500,4e3],TTFB:[800,1800]}[e]||[0,0];return n<=t?"good":n<=r?"needs-improvement":"poor"}(e.name,e.value),r={name:e.name,value:e.value,rating:t,timestamp:Date.now()};d.current.push(r),l&&console.log("".concat(e.name,":"),e.value,"(".concat(t,")")),n=r,"gtag"in window&&window.gtag("event",n.name,{event_category:"Web Vitals",event_label:n.rating,value:Math.round("CLS"===n.name?1e3*n.value:n.value),non_interaction:!0}),fetch("/api/analytics/web-vitals",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(n)}).catch(()=>{})};n(i),t(i),r(i),o(i),a(i)})},[l]),f=(0,r.useCallback)(()=>{if(!a)return;let e=new IntersectionObserver(n=>{n.forEach(n=>{if(n.isIntersecting){let t=n.target;t.dataset.src&&(t.src=t.dataset.src,t.classList.remove("lazy"),t.classList.add("loaded")),t.dataset.srcset&&(t.srcset=t.dataset.srcset),e.unobserve(t)}})},{rootMargin:"50px 0px",threshold:.01});document.querySelectorAll("img[data-src]").forEach(n=>e.observe(n)),m.current.intersection=e},[a]),u=(0,r.useCallback)(()=>{i&&"fonts"in document&&document.fonts.ready.then(()=>{document.body.classList.add("fonts-loaded"),["/fonts/inter-var.woff2","/fonts/inter-bold.woff2"].forEach(e=>{let n=document.createElement("link");n.rel="preload",n.as="font",n.type="font/woff2",n.crossOrigin="anonymous",n.href=e,document.head.appendChild(n)})})},[i]),h=(0,r.useCallback)(()=>{n&&((({"/":["/jogos","/cassino","/sobre"],"/jogos":["/cassino","/guias","/faq"],"/cassino":["/jogos","/promocoes","/sobre"],"/guias":["/faq","/glossario","/jogos"],"/faq":["/suporte","/sobre","/guias"],"/glossario":["/guias","/faq","/jogos"],"/cidades":["/sobre","/faq","/jogos"]})[c]||[]).forEach(e=>{let n=document.createElement("link");n.rel="prefetch",n.href=e,document.head.appendChild(n)}),["https://www.google-analytics.com","https://www.googletagmanager.com","https://fonts.googleapis.com","https://fonts.gstatic.com"].forEach(e=>{let n=document.createElement("link");n.rel="preconnect",n.href=e,e.includes("fonts.gstatic.com")&&(n.crossOrigin="anonymous"),document.head.appendChild(n)}))},[n,c]),p=(0,r.useCallback)(()=>{s&&["/styles/components.css","/styles/animations.css"].forEach(e=>{let n=document.createElement("link");n.rel="preload",n.as="style",n.href=e,n.onload=()=>{n.rel="stylesheet"},document.head.appendChild(n)})},[s]),y=(0,r.useCallback)(()=>{let e=new PerformanceObserver(e=>{e.getEntries().forEach(e=>{l&&console.log("Performance entry:",e),"longtask"===e.entryType&&console.warn("Long task detected:",e.duration),"layout-shift"===e.entryType&&(e.hadRecentInput||console.log("Layout shift:",e.value)),"resource"===e.entryType&&e.duration>1e3&&console.warn("Slow resource:",e.name,e.duration)})});["longtask","layout-shift","resource","navigation"].forEach(n=>{try{e.observe({entryTypes:[n]})}catch(e){l&&console.log("Performance observer type ".concat(n," not supported"),e)}}),m.current.performance=e},[l]);return(0,r.useEffect)(()=>(g(),f(),u(),h(),p(),y(),()=>{let e=m.current;e.intersection&&e.intersection.disconnect(),e.performance&&e.performance.disconnect()}),[g,f,u,h,p,y]),null}},8297:(e,n,t)=>{"use strict";t.d(n,{default:()=>a});var r=t(5155),o=t(3554);function a(e){let{preloadResources:n=[],prefetchUrls:t=[],structuredData:a}=e;return(0,r.jsxs)(r.Fragment,{children:[n.map((e,n)=>(0,r.jsx)("link",{rel:"preload",href:e.href,as:e.as,type:e.type,crossOrigin:e.crossOrigin},n)),t.map((e,n)=>(0,r.jsx)("link",{rel:"prefetch",href:e},n)),(0,r.jsx)("link",{rel:"dns-prefetch",href:"//www.google-analytics.com"}),(0,r.jsx)("link",{rel:"dns-prefetch",href:"//www.googletagmanager.com"}),(0,r.jsx)("link",{rel:"dns-prefetch",href:"//fonts.googleapis.com"}),(0,r.jsx)("link",{rel:"dns-prefetch",href:"//fonts.gstatic.com"}),(0,r.jsx)("link",{rel:"dns-prefetch",href:"//connect.facebook.net"}),(0,r.jsx)("link",{rel:"preconnect",href:"https://fonts.googleapis.com"}),(0,r.jsx)("link",{rel:"preconnect",href:"https://fonts.gstatic.com",crossOrigin:"anonymous"}),(0,r.jsx)("link",{rel:"preconnect",href:"https://www.google-analytics.com"}),a&&(0,r.jsx)(o.default,{id:"structured-data",type:"application/ld+json",dangerouslySetInnerHTML:{__html:JSON.stringify(a)}}),(0,r.jsx)("style",{dangerouslySetInnerHTML:{__html:"\n          /* Critical CSS for immediate rendering */\n          .hero-section {\n            background: linear-gradient(135deg, #1a1a1a 0%, #2d3748 100%);\n            min-height: 100vh;\n            display: flex;\n            align-items: center;\n            justify-content: center;\n          }\n\n          .critical-text {\n            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;\n            color: white;\n            text-align: center;\n          }\n\n          .cta-button {\n            background: linear-gradient(45deg, #f59e0b, #d97706);\n            color: white;\n            padding: 12px 24px;\n            border-radius: 8px;\n            text-decoration: none;\n            font-weight: 600;\n            display: inline-block;\n            margin-top: 16px;\n          }\n        "}}),(0,r.jsx)(o.default,{id:"performance-monitor",strategy:"afterInteractive",children:"\n          // Monitor Core Web Vitals\n          function sendToAnalytics(metric) {\n            if (typeof gtag !== 'undefined') {\n              gtag('event', metric.name, {\n                event_category: 'Web Vitals',\n                event_label: metric.id,\n                value: Math.round(metric.name === 'CLS' ? metric.value * 1000 : metric.value),\n                non_interaction: true,\n              });\n            }\n          }\n\n          // Import web-vitals library dynamically\n          import('web-vitals').then(({ onCLS, onINP, onFCP, onLCP, onTTFB }) => {\n            onCLS(sendToAnalytics);\n            onINP(sendToAnalytics);\n            onFCP(sendToAnalytics);\n            onLCP(sendToAnalytics);\n            onTTFB(sendToAnalytics);\n          });\n\n          // Resource timing monitoring\n          if ('PerformanceObserver' in window) {\n            const observer = new PerformanceObserver((list) => {\n              list.getEntries().forEach((entry) => {\n                if (entry.entryType === 'largest-contentful-paint') {\n                  console.log('LCP:', entry.startTime);\n                }\n                if (entry.entryType === 'first-input') {\n                  console.log('FID:', entry.processingStart - entry.startTime);\n                }\n              });\n            });\n            \n            observer.observe({ entryTypes: ['largest-contentful-paint', 'first-input'] });\n          }\n        "}),(0,r.jsx)(o.default,{id:"sw-registration",strategy:"afterInteractive",children:"\n          if ('serviceWorker' in navigator) {\n            window.addEventListener('load', function() {\n              navigator.serviceWorker.register('/sw.js')\n                .then(function(registration) {\n                  console.log('SW registered: ', registration);\n                })\n                .catch(function(registrationError) {\n                  console.log('SW registration failed: ', registrationError);\n                });\n            });\n          }\n        "}),(0,r.jsx)(o.default,{id:"image-optimization",strategy:"afterInteractive",children:"\n          // Lazy load images with Intersection Observer\n          if ('IntersectionObserver' in window) {\n            const imageObserver = new IntersectionObserver((entries, observer) => {\n              entries.forEach(entry => {\n                if (entry.isIntersecting) {\n                  const img = entry.target;\n                  img.src = img.dataset.src;\n                  img.classList.remove('lazy');\n                  img.classList.add('loaded');\n                  observer.unobserve(img);\n                }\n              });\n            });\n\n            document.querySelectorAll('img[data-src]').forEach(img => {\n              imageObserver.observe(img);\n            });\n          }\n\n          // Preload critical images\n          const criticalImages = [\n            '/images/hero-bg.webp',\n            '/images/logo.webp',\n            '/images/featured-games.webp'\n          ];\n\n          criticalImages.forEach(src => {\n            const link = document.createElement('link');\n            link.rel = 'preload';\n            link.as = 'image';\n            link.href = src;\n            document.head.appendChild(link);\n          });\n        "}),(0,r.jsx)(o.default,{id:"font-optimization",strategy:"afterInteractive",children:"\n          // Font display optimization\n          if ('fonts' in document) {\n            document.fonts.ready.then(() => {\n              document.body.classList.add('fonts-loaded');\n            });\n          }\n        "})]})}},8962:(e,n,t)=>{"use strict";t.d(n,{ABTestProvider:()=>l});var r=t(5155),o=t(2115),a=t(5752);let i=(0,o.createContext)(null),s={hero_cta_buttons:{id:"hero_cta_buttons",name:"Hero CTA Button Variants",isActive:!0,trafficAllocation:1,variants:[{id:"control",name:"Original Buttons",weight:50,config:{primaryText:"Cadastrar Gr\xe1tis",secondaryText:"Ver Jogos",primaryColor:"yellow",layout:"horizontal"}},{id:"variant_a",name:"Urgency Buttons",weight:25,config:{primaryText:"Come\xe7ar Agora - Gr\xe1tis!",secondaryText:"Explorar Jogos",primaryColor:"orange",layout:"horizontal"}},{id:"variant_b",name:"Benefit-focused Buttons",weight:25,config:{primaryText:"Ganhar R$ 5.000 Gr\xe1tis",secondaryText:"Ver Todos os B\xf4nus",primaryColor:"green",layout:"vertical"}}]},banner_timing:{id:"banner_timing",name:"Conversion Banner Timing",isActive:!0,trafficAllocation:.8,variants:[{id:"immediate",name:"Show Immediately",weight:33,config:{delay:0}},{id:"delayed_3s",name:"Show After 3 Seconds",weight:33,config:{delay:3e3}},{id:"delayed_10s",name:"Show After 10 Seconds",weight:34,config:{delay:1e4}}]},game_card_layout:{id:"game_card_layout",name:"Game Card Layout Test",isActive:!0,trafficAllocation:.5,variants:[{id:"standard",name:"Standard Layout",weight:50,config:{showRating:!0,showProvider:!0,buttonStyle:"overlay"}},{id:"minimal",name:"Minimal Layout",weight:50,config:{showRating:!1,showProvider:!1,buttonStyle:"bottom"}}]}};function l(e){let{children:n}=e,[t,l]=(0,o.useState)({}),[c,d]=(0,o.useState)(!1);(0,o.useEffect)(()=>{let e=localStorage.getItem("ab_tests"),n={};if(e)try{n=JSON.parse(e)}catch(e){console.error("Error parsing stored A/B tests:",e)}Object.values(s).forEach(e=>{if(!e.isActive||n[e.id]||Math.random()>e.trafficAllocation)return;let t=Math.random()*e.variants.reduce((e,n)=>e+n.weight,0);for(let r of e.variants)if((t-=r.weight)<=0){n[e.id]=r.id;break}}),l(n),localStorage.setItem("ab_tests",JSON.stringify(n)),d(!0),Object.entries(n).forEach(e=>{let[n,t]=e;(0,a.yM)({type:"view",affiliateId:"main_casino",metadata:{ab_test:n,variant:t,event:"test_assignment"}})})},[]);let m=e=>{if(!c)return null;let n=s[e],r=t[e];return n&&r&&n.variants.find(e=>e.id===r)||null};return(0,r.jsx)(i.Provider,{value:{getVariant:m,trackEvent:(e,n,t)=>{let r=m(e);r&&(0,a.yM)({type:"click",affiliateId:"main_casino",value:t,metadata:{ab_test:e,variant:r.id,event:n}})},isInTest:e=>!!t[e]},children:n})}}},e=>{var n=n=>e(e.s=n);e.O(0,[96,690,76,358],()=>n(480)),_N_E=e.O()}]);