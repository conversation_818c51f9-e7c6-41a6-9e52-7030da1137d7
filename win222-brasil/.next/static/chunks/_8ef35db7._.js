(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/components/WebVitals.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "PerformanceMonitor": (()=>PerformanceMonitor),
    "default": (()=>WebVitals),
    "useWebVitals": (()=>useWebVitals)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$web$2d$vitals$2f$dist$2f$web$2d$vitals$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/web-vitals/dist/web-vitals.js [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature(), _s2 = __turbopack_context__.k.signature();
'use client';
;
;
function WebVitals({ debug = false }) {
    _s();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "WebVitals.useEffect": ()=>{
            // Função para enviar métricas para analytics
            const sendToAnalytics = {
                "WebVitals.useEffect.sendToAnalytics": (metric)=>{
                    if (debug) {
                        console.log('Web Vital:', metric);
                    }
                    // Enviar para Google Analytics 4
                    if ("object" !== 'undefined' && window.gtag) {
                        window.gtag('event', metric.name, {
                            event_category: 'Web Vitals',
                            event_label: metric.id,
                            value: Math.round(metric.name === 'CLS' ? metric.value * 1000 : metric.value),
                            non_interaction: true
                        });
                    }
                    // Enviar para endpoint personalizado (opcional)
                    if ("TURBOPACK compile-time falsy", 0) {
                        "TURBOPACK unreachable";
                    }
                }
            }["WebVitals.useEffect.sendToAnalytics"];
            // Coletar métricas Core Web Vitals
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$web$2d$vitals$2f$dist$2f$web$2d$vitals$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["onCLS"])(sendToAnalytics);
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$web$2d$vitals$2f$dist$2f$web$2d$vitals$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["onINP"])(sendToAnalytics);
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$web$2d$vitals$2f$dist$2f$web$2d$vitals$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["onFCP"])(sendToAnalytics);
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$web$2d$vitals$2f$dist$2f$web$2d$vitals$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["onLCP"])(sendToAnalytics);
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$web$2d$vitals$2f$dist$2f$web$2d$vitals$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["onTTFB"])(sendToAnalytics);
            // Otimizações adicionais
            const optimizePerformance = {
                "WebVitals.useEffect.optimizePerformance": ()=>{
                    // Preload de recursos críticos
                    const preloadCriticalResources = {
                        "WebVitals.useEffect.optimizePerformance.preloadCriticalResources": ()=>{
                            const criticalImages = [
                                '/images/logo.png',
                                '/images/hero-banner.jpg',
                                '/images/og-image.jpg'
                            ];
                            criticalImages.forEach({
                                "WebVitals.useEffect.optimizePerformance.preloadCriticalResources": (src)=>{
                                    const link = document.createElement('link');
                                    link.rel = 'preload';
                                    link.as = 'image';
                                    link.href = src;
                                    document.head.appendChild(link);
                                }
                            }["WebVitals.useEffect.optimizePerformance.preloadCriticalResources"]);
                        }
                    }["WebVitals.useEffect.optimizePerformance.preloadCriticalResources"];
                    // Lazy loading para imagens não críticas
                    const setupLazyLoading = {
                        "WebVitals.useEffect.optimizePerformance.setupLazyLoading": ()=>{
                            if ('IntersectionObserver' in window) {
                                const imageObserver = new IntersectionObserver({
                                    "WebVitals.useEffect.optimizePerformance.setupLazyLoading": (entries, observer)=>{
                                        entries.forEach({
                                            "WebVitals.useEffect.optimizePerformance.setupLazyLoading": (entry)=>{
                                                if (entry.isIntersecting) {
                                                    const img = entry.target;
                                                    if (img.dataset.src) {
                                                        img.src = img.dataset.src;
                                                        img.classList.remove('lazy');
                                                        observer.unobserve(img);
                                                    }
                                                }
                                            }
                                        }["WebVitals.useEffect.optimizePerformance.setupLazyLoading"]);
                                    }
                                }["WebVitals.useEffect.optimizePerformance.setupLazyLoading"]);
                                document.querySelectorAll('img[data-src]').forEach({
                                    "WebVitals.useEffect.optimizePerformance.setupLazyLoading": (img)=>{
                                        imageObserver.observe(img);
                                    }
                                }["WebVitals.useEffect.optimizePerformance.setupLazyLoading"]);
                            }
                        }
                    }["WebVitals.useEffect.optimizePerformance.setupLazyLoading"];
                    // Otimizar fontes
                    const optimizeFonts = {
                        "WebVitals.useEffect.optimizePerformance.optimizeFonts": ()=>{
                            // Preload de fontes críticas
                            const fontPreloads = [
                                'https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuLyfAZ9hiA.woff2',
                                'https://fonts.gstatic.com/s/roboto/v30/KFOmCnqEu92Fr1Mu4mxKKTU1Kg.woff2'
                            ];
                            fontPreloads.forEach({
                                "WebVitals.useEffect.optimizePerformance.optimizeFonts": (href)=>{
                                    const link = document.createElement('link');
                                    link.rel = 'preload';
                                    link.as = 'font';
                                    link.type = 'font/woff2';
                                    link.crossOrigin = 'anonymous';
                                    link.href = href;
                                    document.head.appendChild(link);
                                }
                            }["WebVitals.useEffect.optimizePerformance.optimizeFonts"]);
                        }
                    }["WebVitals.useEffect.optimizePerformance.optimizeFonts"];
                    // Executar otimizações
                    preloadCriticalResources();
                    setupLazyLoading();
                    optimizeFonts();
                    // Otimizar scroll performance
                    let ticking = false;
                    const updateScrollPosition = {
                        "WebVitals.useEffect.optimizePerformance.updateScrollPosition": ()=>{
                            ticking = false;
                        // Lógica de scroll otimizada aqui
                        }
                    }["WebVitals.useEffect.optimizePerformance.updateScrollPosition"];
                    const requestScrollUpdate = {
                        "WebVitals.useEffect.optimizePerformance.requestScrollUpdate": ()=>{
                            if (!ticking) {
                                requestAnimationFrame(updateScrollPosition);
                                ticking = true;
                            }
                        }
                    }["WebVitals.useEffect.optimizePerformance.requestScrollUpdate"];
                    window.addEventListener('scroll', requestScrollUpdate, {
                        passive: true
                    });
                    // Cleanup
                    return ({
                        "WebVitals.useEffect.optimizePerformance": ()=>{
                            window.removeEventListener('scroll', requestScrollUpdate);
                        }
                    })["WebVitals.useEffect.optimizePerformance"];
                }
            }["WebVitals.useEffect.optimizePerformance"];
            // Executar otimizações após o carregamento
            if (document.readyState === 'complete') {
                optimizePerformance();
            } else {
                window.addEventListener('load', optimizePerformance);
            }
            // Service Worker para cache
            if ('serviceWorker' in navigator && ("TURBOPACK compile-time value", "development") === 'production') {
                "TURBOPACK unreachable";
            }
        }
    }["WebVitals.useEffect"], [
        debug
    ]);
    return null; // Este componente não renderiza nada visível
}
_s(WebVitals, "OD7bBpZva5O2jO+Puf00hKivP7c=");
_c = WebVitals;
function useWebVitals(callback) {
    _s1();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useWebVitals.useEffect": ()=>{
            const handleMetric = {
                "useWebVitals.useEffect.handleMetric": (metric)=>{
                    if (callback) {
                        callback(metric);
                    }
                }
            }["useWebVitals.useEffect.handleMetric"];
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$web$2d$vitals$2f$dist$2f$web$2d$vitals$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["onCLS"])(handleMetric);
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$web$2d$vitals$2f$dist$2f$web$2d$vitals$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["onINP"])(handleMetric);
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$web$2d$vitals$2f$dist$2f$web$2d$vitals$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["onFCP"])(handleMetric);
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$web$2d$vitals$2f$dist$2f$web$2d$vitals$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["onLCP"])(handleMetric);
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$web$2d$vitals$2f$dist$2f$web$2d$vitals$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["onTTFB"])(handleMetric);
        }
    }["useWebVitals.useEffect"], [
        callback
    ]);
}
_s1(useWebVitals, "OD7bBpZva5O2jO+Puf00hKivP7c=");
function PerformanceMonitor() {
    _s2();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "PerformanceMonitor.useEffect": ()=>{
            const observer = new PerformanceObserver({
                "PerformanceMonitor.useEffect": (list)=>{
                    list.getEntries().forEach({
                        "PerformanceMonitor.useEffect": (entry)=>{
                            if (entry.entryType === 'navigation') {
                                const navEntry = entry;
                                console.log('Navigation timing:', {
                                    domContentLoaded: navEntry.domContentLoadedEventEnd - navEntry.domContentLoadedEventStart,
                                    loadComplete: navEntry.loadEventEnd - navEntry.loadEventStart,
                                    firstByte: navEntry.responseStart - navEntry.requestStart
                                });
                            }
                        }
                    }["PerformanceMonitor.useEffect"]);
                }
            }["PerformanceMonitor.useEffect"]);
            observer.observe({
                entryTypes: [
                    'navigation',
                    'paint',
                    'largest-contentful-paint'
                ]
            });
            return ({
                "PerformanceMonitor.useEffect": ()=>observer.disconnect()
            })["PerformanceMonitor.useEffect"];
        }
    }["PerformanceMonitor.useEffect"], []);
    return null;
}
_s2(PerformanceMonitor, "OD7bBpZva5O2jO+Puf00hKivP7c=");
_c1 = PerformanceMonitor;
var _c, _c1;
__turbopack_context__.k.register(_c, "WebVitals");
__turbopack_context__.k.register(_c1, "PerformanceMonitor");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/affiliate.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * Affiliate Marketing Management System
 * Handles affiliate links, tracking, and conversion optimization
 */ __turbopack_context__.s({
    "AFFILIATE_LINKS": (()=>AFFILIATE_LINKS),
    "createSEOFriendlyLink": (()=>createSEOFriendlyLink),
    "generateAffiliateUrl": (()=>generateAffiliateUrl),
    "getABTestAffiliate": (()=>getABTestAffiliate),
    "getAffiliatesByCategory": (()=>getAffiliatesByCategory),
    "getPrimaryAffiliate": (()=>getPrimaryAffiliate),
    "trackAffiliateClick": (()=>trackAffiliateClick),
    "trackConversion": (()=>trackConversion)
});
const AFFILIATE_LINKS = {
    // Main Casino Platform
    main_casino: {
        id: 'main_casino',
        name: 'Win222 Brasil - Cassino Principal',
        url: 'https://win222brasil.com/register',
        category: 'casino',
        priority: 1,
        isActive: true,
        trackingParams: {
            utm_source: 'win222brasil',
            utm_medium: 'website',
            utm_campaign: 'main_casino',
            ref: 'win222br'
        },
        conversionGoals: [
            'signup',
            'deposit'
        ]
    },
    // Sports Betting
    sports_betting: {
        id: 'sports_betting',
        name: 'Win222 Brasil - Apostas Esportivas',
        url: 'https://win222brasil.com/sports',
        category: 'sports',
        priority: 1,
        isActive: true,
        trackingParams: {
            utm_source: 'win222brasil',
            utm_medium: 'website',
            utm_campaign: 'sports_betting',
            ref: 'win222br_sports'
        },
        conversionGoals: [
            'signup',
            'bet'
        ]
    },
    // Slots Games
    slots_games: {
        id: 'slots_games',
        name: 'Win222 Brasil - Slots Premium',
        url: 'https://win222brasil.com/slots',
        category: 'slots',
        priority: 2,
        isActive: true,
        trackingParams: {
            utm_source: 'win222brasil',
            utm_medium: 'website',
            utm_campaign: 'slots_games',
            ref: 'win222br_slots'
        },
        conversionGoals: [
            'play',
            'deposit'
        ]
    },
    // Live Casino
    live_casino: {
        id: 'live_casino',
        name: 'Win222 Brasil - Cassino Ao Vivo',
        url: 'https://win222brasil.com/live',
        category: 'live',
        priority: 2,
        isActive: true,
        trackingParams: {
            utm_source: 'win222brasil',
            utm_medium: 'website',
            utm_campaign: 'live_casino',
            ref: 'win222br_live'
        },
        conversionGoals: [
            'play',
            'deposit'
        ]
    },
    // Welcome Bonus
    welcome_bonus: {
        id: 'welcome_bonus',
        name: 'Win222 Brasil - Bônus de Boas-vindas',
        url: 'https://win222brasil.com/bonus',
        category: 'bonus',
        priority: 1,
        isActive: true,
        trackingParams: {
            utm_source: 'win222brasil',
            utm_medium: 'website',
            utm_campaign: 'welcome_bonus',
            ref: 'win222br_bonus'
        },
        conversionGoals: [
            'signup',
            'claim_bonus'
        ]
    },
    // Poker Games
    poker_games: {
        id: 'poker_games',
        name: 'Win222 Brasil - Poker Online',
        url: 'https://win222brasil.com/poker',
        category: 'poker',
        priority: 3,
        isActive: true,
        trackingParams: {
            utm_source: 'win222brasil',
            utm_medium: 'website',
            utm_campaign: 'poker_games',
            ref: 'win222br_poker'
        },
        conversionGoals: [
            'play',
            'tournament'
        ]
    }
};
function generateAffiliateUrl(affiliateId, additionalParams) {
    const affiliate = AFFILIATE_LINKS[affiliateId];
    if (!affiliate || !affiliate.isActive) {
        return '#';
    }
    const url = new URL(affiliate.url);
    // Add tracking parameters
    if (affiliate.trackingParams) {
        Object.entries(affiliate.trackingParams).forEach(([key, value])=>{
            url.searchParams.set(key, value);
        });
    }
    // Add additional parameters
    if (additionalParams) {
        Object.entries(additionalParams).forEach(([key, value])=>{
            url.searchParams.set(key, value);
        });
    }
    // Add timestamp for unique tracking
    url.searchParams.set('t', Date.now().toString());
    return url.toString();
}
function trackConversion(event) {
    // Send to analytics
    if ("object" !== 'undefined' && window.gtag) {
        window.gtag('event', 'conversion', {
            event_category: 'affiliate',
            event_label: event.affiliateId,
            value: event.value || 0,
            custom_parameters: {
                conversion_type: event.type,
                affiliate_id: event.affiliateId,
                ...event.metadata
            }
        });
    }
    // Send to custom analytics endpoint
    if ("TURBOPACK compile-time truthy", 1) {
        fetch('/api/analytics/conversion', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                ...event,
                timestamp: new Date().toISOString(),
                userAgent: navigator.userAgent,
                referrer: document.referrer,
                url: window.location.href
            })
        }).catch(console.error);
    }
}
function getAffiliatesByCategory(category) {
    return Object.values(AFFILIATE_LINKS).filter((link)=>link.category === category && link.isActive).sort((a, b)=>a.priority - b.priority);
}
function getPrimaryAffiliate(category) {
    const affiliates = getAffiliatesByCategory(category);
    return affiliates.length > 0 ? affiliates[0] : null;
}
function getABTestAffiliate(category) {
    const affiliates = getAffiliatesByCategory(category);
    if (affiliates.length === 0) return null;
    // Simple A/B testing based on user session
    const sessionId = ("TURBOPACK compile-time truthy", 1) ? sessionStorage.getItem('ab_test_id') || Math.random().toString(36) : ("TURBOPACK unreachable", undefined);
    if ("TURBOPACK compile-time truthy", 1) {
        sessionStorage.setItem('ab_test_id', sessionId);
    }
    const hash = sessionId.split('').reduce((a, b)=>{
        a = (a << 5) - a + b.charCodeAt(0);
        return a & a;
    }, 0);
    const index = Math.abs(hash) % affiliates.length;
    return affiliates[index];
}
function trackAffiliateClick(affiliateId, context) {
    trackConversion({
        type: 'click',
        affiliateId,
        metadata: {
            context
        }
    });
}
function createSEOFriendlyLink(affiliateId, text, className, additionalParams) {
    const url = generateAffiliateUrl(affiliateId, additionalParams);
    return {
        href: url,
        onClick: ()=>trackAffiliateClick(affiliateId),
        rel: 'noopener noreferrer sponsored',
        target: '_blank',
        className,
        children: text
    };
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/ABTestProvider.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "ABTestProvider": (()=>ABTestProvider),
    "useABTest": (()=>useABTest),
    "useABTestVariant": (()=>useABTestVariant)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$affiliate$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/affiliate.ts [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature(), _s2 = __turbopack_context__.k.signature();
'use client';
;
;
const ABTestContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])(null);
// A/B Test configurations
const AB_TESTS = {
    hero_cta_buttons: {
        id: 'hero_cta_buttons',
        name: 'Hero CTA Button Variants',
        isActive: true,
        trafficAllocation: 1.0,
        variants: [
            {
                id: 'control',
                name: 'Original Buttons',
                weight: 50,
                config: {
                    primaryText: 'Cadastrar Grátis',
                    secondaryText: 'Ver Jogos',
                    primaryColor: 'yellow',
                    layout: 'horizontal'
                }
            },
            {
                id: 'variant_a',
                name: 'Urgency Buttons',
                weight: 25,
                config: {
                    primaryText: 'Começar Agora - Grátis!',
                    secondaryText: 'Explorar Jogos',
                    primaryColor: 'orange',
                    layout: 'horizontal'
                }
            },
            {
                id: 'variant_b',
                name: 'Benefit-focused Buttons',
                weight: 25,
                config: {
                    primaryText: 'Ganhar R$ 5.000 Grátis',
                    secondaryText: 'Ver Todos os Bônus',
                    primaryColor: 'green',
                    layout: 'vertical'
                }
            }
        ]
    },
    banner_timing: {
        id: 'banner_timing',
        name: 'Conversion Banner Timing',
        isActive: true,
        trafficAllocation: 0.8,
        variants: [
            {
                id: 'immediate',
                name: 'Show Immediately',
                weight: 33,
                config: {
                    delay: 0
                }
            },
            {
                id: 'delayed_3s',
                name: 'Show After 3 Seconds',
                weight: 33,
                config: {
                    delay: 3000
                }
            },
            {
                id: 'delayed_10s',
                name: 'Show After 10 Seconds',
                weight: 34,
                config: {
                    delay: 10000
                }
            }
        ]
    },
    game_card_layout: {
        id: 'game_card_layout',
        name: 'Game Card Layout Test',
        isActive: true,
        trafficAllocation: 0.5,
        variants: [
            {
                id: 'standard',
                name: 'Standard Layout',
                weight: 50,
                config: {
                    showRating: true,
                    showProvider: true,
                    buttonStyle: 'overlay'
                }
            },
            {
                id: 'minimal',
                name: 'Minimal Layout',
                weight: 50,
                config: {
                    showRating: false,
                    showProvider: false,
                    buttonStyle: 'bottom'
                }
            }
        ]
    }
};
function ABTestProvider({ children }) {
    _s();
    const [userTests, setUserTests] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({});
    const [isInitialized, setIsInitialized] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "ABTestProvider.useEffect": ()=>{
            // Initialize user's test assignments
            const initializeTests = {
                "ABTestProvider.useEffect.initializeTests": ()=>{
                    const storedTests = localStorage.getItem('ab_tests');
                    let tests = {};
                    if (storedTests) {
                        try {
                            tests = JSON.parse(storedTests);
                        } catch (error) {
                            console.error('Error parsing stored A/B tests:', error);
                        }
                    }
                    // Assign user to tests they're not already in
                    Object.values(AB_TESTS).forEach({
                        "ABTestProvider.useEffect.initializeTests": (test)=>{
                            if (!test.isActive) return;
                            if (tests[test.id]) return; // Already assigned
                            // Check if user should be included in this test
                            if (Math.random() > test.trafficAllocation) return;
                            // Select variant based on weights
                            const totalWeight = test.variants.reduce({
                                "ABTestProvider.useEffect.initializeTests.totalWeight": (sum, variant)=>sum + variant.weight
                            }["ABTestProvider.useEffect.initializeTests.totalWeight"], 0);
                            let random = Math.random() * totalWeight;
                            for (const variant of test.variants){
                                random -= variant.weight;
                                if (random <= 0) {
                                    tests[test.id] = variant.id;
                                    break;
                                }
                            }
                        }
                    }["ABTestProvider.useEffect.initializeTests"]);
                    setUserTests(tests);
                    localStorage.setItem('ab_tests', JSON.stringify(tests));
                    setIsInitialized(true);
                    // Track test assignments
                    Object.entries(tests).forEach({
                        "ABTestProvider.useEffect.initializeTests": ([testId, variantId])=>{
                            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$affiliate$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["trackConversion"])({
                                type: 'view',
                                affiliateId: 'main_casino',
                                metadata: {
                                    ab_test: testId,
                                    variant: variantId,
                                    event: 'test_assignment'
                                }
                            });
                        }
                    }["ABTestProvider.useEffect.initializeTests"]);
                }
            }["ABTestProvider.useEffect.initializeTests"];
            initializeTests();
        }
    }["ABTestProvider.useEffect"], []);
    const getVariant = (testId)=>{
        if (!isInitialized) return null;
        const test = AB_TESTS[testId];
        const variantId = userTests[testId];
        if (!test || !variantId) return null;
        return test.variants.find((v)=>v.id === variantId) || null;
    };
    const trackEvent = (testId, event, value)=>{
        const variant = getVariant(testId);
        if (!variant) return;
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$affiliate$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["trackConversion"])({
            type: 'click',
            affiliateId: 'main_casino',
            value,
            metadata: {
                ab_test: testId,
                variant: variant.id,
                event
            }
        });
    };
    const isInTest = (testId)=>{
        return !!userTests[testId];
    };
    const contextValue = {
        getVariant,
        trackEvent,
        isInTest
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(ABTestContext.Provider, {
        value: contextValue,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/components/ABTestProvider.tsx",
        lineNumber: 228,
        columnNumber: 5
    }, this);
}
_s(ABTestProvider, "Rh4DB6AC+XV89YziNlAEdqoeatc=");
_c = ABTestProvider;
function useABTest() {
    _s1();
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(ABTestContext);
    if (!context) {
        throw new Error('useABTest must be used within ABTestProvider');
    }
    return context;
}
_s1(useABTest, "b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=");
function useABTestVariant(testId) {
    _s2();
    const { getVariant, trackEvent, isInTest } = useABTest();
    const variant = getVariant(testId);
    const config = variant?.config || {};
    const track = (event, value)=>{
        trackEvent(testId, event, value);
    };
    return {
        variant,
        config,
        track,
        isInTest: isInTest(testId)
    };
}
_s2(useABTestVariant, "EJJIk2g2uOcSIR+zr1xcGGUvW4w=", false, function() {
    return [
        useABTest
    ];
});
var _c;
__turbopack_context__.k.register(_c, "ABTestProvider");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/node_modules/next/dist/client/set-attributes-from-props.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "setAttributesFromProps", {
    enumerable: true,
    get: function() {
        return setAttributesFromProps;
    }
});
const DOMAttributeNames = {
    acceptCharset: 'accept-charset',
    className: 'class',
    htmlFor: 'for',
    httpEquiv: 'http-equiv',
    noModule: 'noModule'
};
const ignoreProps = [
    'onLoad',
    'onReady',
    'dangerouslySetInnerHTML',
    'children',
    'onError',
    'strategy',
    'stylesheets'
];
function isBooleanScriptAttribute(attr) {
    return [
        'async',
        'defer',
        'noModule'
    ].includes(attr);
}
function setAttributesFromProps(el, props) {
    for (const [p, value] of Object.entries(props)){
        if (!props.hasOwnProperty(p)) continue;
        if (ignoreProps.includes(p)) continue;
        // we don't render undefined props to the DOM
        if (value === undefined) {
            continue;
        }
        const attr = DOMAttributeNames[p] || p.toLowerCase();
        if (el.tagName === 'SCRIPT' && isBooleanScriptAttribute(attr)) {
            // Correctly assign boolean script attributes
            // https://github.com/vercel/next.js/pull/20748
            ;
            el[attr] = !!value;
        } else {
            el.setAttribute(attr, String(value));
        }
        // Remove falsy non-zero boolean attributes so they are correctly interpreted
        // (e.g. if we set them to false, this coerces to the string "false", which the browser interprets as true)
        if (value === false || el.tagName === 'SCRIPT' && isBooleanScriptAttribute(attr) && (!value || value === 'false')) {
            // Call setAttribute before, as we need to set and unset the attribute to override force async:
            // https://html.spec.whatwg.org/multipage/scripting.html#script-force-async
            el.setAttribute(attr, '');
            el.removeAttribute(attr);
        }
    }
}
if ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {
    Object.defineProperty(exports.default, '__esModule', {
        value: true
    });
    Object.assign(exports.default, exports);
    module.exports = exports.default;
} //# sourceMappingURL=set-attributes-from-props.js.map
}}),
"[project]/node_modules/next/dist/client/request-idle-callback.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
0 && (module.exports = {
    cancelIdleCallback: null,
    requestIdleCallback: null
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: all[name]
    });
}
_export(exports, {
    cancelIdleCallback: function() {
        return cancelIdleCallback;
    },
    requestIdleCallback: function() {
        return requestIdleCallback;
    }
});
const requestIdleCallback = typeof self !== 'undefined' && self.requestIdleCallback && self.requestIdleCallback.bind(window) || function(cb) {
    let start = Date.now();
    return self.setTimeout(function() {
        cb({
            didTimeout: false,
            timeRemaining: function() {
                return Math.max(0, 50 - (Date.now() - start));
            }
        });
    }, 1);
};
const cancelIdleCallback = typeof self !== 'undefined' && self.cancelIdleCallback && self.cancelIdleCallback.bind(window) || function(id) {
    return clearTimeout(id);
};
if ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {
    Object.defineProperty(exports.default, '__esModule', {
        value: true
    });
    Object.assign(exports.default, exports);
    module.exports = exports.default;
} //# sourceMappingURL=request-idle-callback.js.map
}}),
"[project]/node_modules/next/dist/client/script.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use client';
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
0 && (module.exports = {
    default: null,
    handleClientScriptLoad: null,
    initScriptLoader: null
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: all[name]
    });
}
_export(exports, {
    default: function() {
        return _default;
    },
    handleClientScriptLoad: function() {
        return handleClientScriptLoad;
    },
    initScriptLoader: function() {
        return initScriptLoader;
    }
});
const _interop_require_default = __turbopack_context__.r("[project]/node_modules/@swc/helpers/cjs/_interop_require_default.cjs [app-client] (ecmascript)");
const _interop_require_wildcard = __turbopack_context__.r("[project]/node_modules/@swc/helpers/cjs/_interop_require_wildcard.cjs [app-client] (ecmascript)");
const _jsxruntime = __turbopack_context__.r("[project]/node_modules/next/dist/compiled/react/jsx-runtime.js [app-client] (ecmascript)");
const _reactdom = /*#__PURE__*/ _interop_require_default._(__turbopack_context__.r("[project]/node_modules/next/dist/compiled/react-dom/index.js [app-client] (ecmascript)"));
const _react = /*#__PURE__*/ _interop_require_wildcard._(__turbopack_context__.r("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)"));
const _headmanagercontextsharedruntime = __turbopack_context__.r("[project]/node_modules/next/dist/shared/lib/head-manager-context.shared-runtime.js [app-client] (ecmascript)");
const _setattributesfromprops = __turbopack_context__.r("[project]/node_modules/next/dist/client/set-attributes-from-props.js [app-client] (ecmascript)");
const _requestidlecallback = __turbopack_context__.r("[project]/node_modules/next/dist/client/request-idle-callback.js [app-client] (ecmascript)");
const ScriptCache = new Map();
const LoadCache = new Set();
const insertStylesheets = (stylesheets)=>{
    // Case 1: Styles for afterInteractive/lazyOnload with appDir injected via handleClientScriptLoad
    //
    // Using ReactDOM.preinit to feature detect appDir and inject styles
    // Stylesheets might have already been loaded if initialized with Script component
    // Re-inject styles here to handle scripts loaded via handleClientScriptLoad
    // ReactDOM.preinit handles dedup and ensures the styles are loaded only once
    if (_reactdom.default.preinit) {
        stylesheets.forEach((stylesheet)=>{
            _reactdom.default.preinit(stylesheet, {
                as: 'style'
            });
        });
        return;
    }
    // Case 2: Styles for afterInteractive/lazyOnload with pages injected via handleClientScriptLoad
    //
    // We use this function to load styles when appdir is not detected
    // TODO: Use React float APIs to load styles once available for pages dir
    if (typeof window !== 'undefined') {
        let head = document.head;
        stylesheets.forEach((stylesheet)=>{
            let link = document.createElement('link');
            link.type = 'text/css';
            link.rel = 'stylesheet';
            link.href = stylesheet;
            head.appendChild(link);
        });
    }
};
const loadScript = (props)=>{
    const { src, id, onLoad = ()=>{}, onReady = null, dangerouslySetInnerHTML, children = '', strategy = 'afterInteractive', onError, stylesheets } = props;
    const cacheKey = id || src;
    // Script has already loaded
    if (cacheKey && LoadCache.has(cacheKey)) {
        return;
    }
    // Contents of this script are already loading/loaded
    if (ScriptCache.has(src)) {
        LoadCache.add(cacheKey);
        // It is possible that multiple `next/script` components all have same "src", but has different "onLoad"
        // This is to make sure the same remote script will only load once, but "onLoad" are executed in order
        ScriptCache.get(src).then(onLoad, onError);
        return;
    }
    /** Execute after the script first loaded */ const afterLoad = ()=>{
        // Run onReady for the first time after load event
        if (onReady) {
            onReady();
        }
        // add cacheKey to LoadCache when load successfully
        LoadCache.add(cacheKey);
    };
    const el = document.createElement('script');
    const loadPromise = new Promise((resolve, reject)=>{
        el.addEventListener('load', function(e) {
            resolve();
            if (onLoad) {
                onLoad.call(this, e);
            }
            afterLoad();
        });
        el.addEventListener('error', function(e) {
            reject(e);
        });
    }).catch(function(e) {
        if (onError) {
            onError(e);
        }
    });
    if (dangerouslySetInnerHTML) {
        // Casting since lib.dom.d.ts doesn't have TrustedHTML yet.
        el.innerHTML = dangerouslySetInnerHTML.__html || '';
        afterLoad();
    } else if (children) {
        el.textContent = typeof children === 'string' ? children : Array.isArray(children) ? children.join('') : '';
        afterLoad();
    } else if (src) {
        el.src = src;
        // do not add cacheKey into LoadCache for remote script here
        // cacheKey will be added to LoadCache when it is actually loaded (see loadPromise above)
        ScriptCache.set(src, loadPromise);
    }
    (0, _setattributesfromprops.setAttributesFromProps)(el, props);
    if (strategy === 'worker') {
        el.setAttribute('type', 'text/partytown');
    }
    el.setAttribute('data-nscript', strategy);
    // Load styles associated with this script
    if (stylesheets) {
        insertStylesheets(stylesheets);
    }
    document.body.appendChild(el);
};
function handleClientScriptLoad(props) {
    const { strategy = 'afterInteractive' } = props;
    if (strategy === 'lazyOnload') {
        window.addEventListener('load', ()=>{
            (0, _requestidlecallback.requestIdleCallback)(()=>loadScript(props));
        });
    } else {
        loadScript(props);
    }
}
function loadLazyScript(props) {
    if (document.readyState === 'complete') {
        (0, _requestidlecallback.requestIdleCallback)(()=>loadScript(props));
    } else {
        window.addEventListener('load', ()=>{
            (0, _requestidlecallback.requestIdleCallback)(()=>loadScript(props));
        });
    }
}
function addBeforeInteractiveToCache() {
    const scripts = [
        ...document.querySelectorAll('[data-nscript="beforeInteractive"]'),
        ...document.querySelectorAll('[data-nscript="beforePageRender"]')
    ];
    scripts.forEach((script)=>{
        const cacheKey = script.id || script.getAttribute('src');
        LoadCache.add(cacheKey);
    });
}
function initScriptLoader(scriptLoaderItems) {
    scriptLoaderItems.forEach(handleClientScriptLoad);
    addBeforeInteractiveToCache();
}
/**
 * Load a third-party scripts in an optimized way.
 *
 * Read more: [Next.js Docs: `next/script`](https://nextjs.org/docs/app/api-reference/components/script)
 */ function Script(props) {
    const { id, src = '', onLoad = ()=>{}, onReady = null, strategy = 'afterInteractive', onError, stylesheets, ...restProps } = props;
    // Context is available only during SSR
    const { updateScripts, scripts, getIsSsr, appDir, nonce } = (0, _react.useContext)(_headmanagercontextsharedruntime.HeadManagerContext);
    /**
   * - First mount:
   *   1. The useEffect for onReady executes
   *   2. hasOnReadyEffectCalled.current is false, but the script hasn't loaded yet (not in LoadCache)
   *      onReady is skipped, set hasOnReadyEffectCalled.current to true
   *   3. The useEffect for loadScript executes
   *   4. hasLoadScriptEffectCalled.current is false, loadScript executes
   *      Once the script is loaded, the onLoad and onReady will be called by then
   *   [If strict mode is enabled / is wrapped in <OffScreen /> component]
   *   5. The useEffect for onReady executes again
   *   6. hasOnReadyEffectCalled.current is true, so entire effect is skipped
   *   7. The useEffect for loadScript executes again
   *   8. hasLoadScriptEffectCalled.current is true, so entire effect is skipped
   *
   * - Second mount:
   *   1. The useEffect for onReady executes
   *   2. hasOnReadyEffectCalled.current is false, but the script has already loaded (found in LoadCache)
   *      onReady is called, set hasOnReadyEffectCalled.current to true
   *   3. The useEffect for loadScript executes
   *   4. The script is already loaded, loadScript bails out
   *   [If strict mode is enabled / is wrapped in <OffScreen /> component]
   *   5. The useEffect for onReady executes again
   *   6. hasOnReadyEffectCalled.current is true, so entire effect is skipped
   *   7. The useEffect for loadScript executes again
   *   8. hasLoadScriptEffectCalled.current is true, so entire effect is skipped
   */ const hasOnReadyEffectCalled = (0, _react.useRef)(false);
    (0, _react.useEffect)(()=>{
        const cacheKey = id || src;
        if (!hasOnReadyEffectCalled.current) {
            // Run onReady if script has loaded before but component is re-mounted
            if (onReady && cacheKey && LoadCache.has(cacheKey)) {
                onReady();
            }
            hasOnReadyEffectCalled.current = true;
        }
    }, [
        onReady,
        id,
        src
    ]);
    const hasLoadScriptEffectCalled = (0, _react.useRef)(false);
    (0, _react.useEffect)(()=>{
        if (!hasLoadScriptEffectCalled.current) {
            if (strategy === 'afterInteractive') {
                loadScript(props);
            } else if (strategy === 'lazyOnload') {
                loadLazyScript(props);
            }
            hasLoadScriptEffectCalled.current = true;
        }
    }, [
        props,
        strategy
    ]);
    if (strategy === 'beforeInteractive' || strategy === 'worker') {
        if (updateScripts) {
            scripts[strategy] = (scripts[strategy] || []).concat([
                {
                    id,
                    src,
                    onLoad,
                    onReady,
                    onError,
                    ...restProps
                }
            ]);
            updateScripts(scripts);
        } else if (getIsSsr && getIsSsr()) {
            // Script has already loaded during SSR
            LoadCache.add(id || src);
        } else if (getIsSsr && !getIsSsr()) {
            loadScript(props);
        }
    }
    // For the app directory, we need React Float to preload these scripts.
    if (appDir) {
        // Injecting stylesheets here handles beforeInteractive and worker scripts correctly
        // For other strategies injecting here ensures correct stylesheet order
        // ReactDOM.preinit handles loading the styles in the correct order,
        // also ensures the stylesheet is loaded only once and in a consistent manner
        //
        // Case 1: Styles for beforeInteractive/worker with appDir - handled here
        // Case 2: Styles for beforeInteractive/worker with pages dir - Not handled yet
        // Case 3: Styles for afterInteractive/lazyOnload with appDir - handled here
        // Case 4: Styles for afterInteractive/lazyOnload with pages dir - handled in insertStylesheets function
        if (stylesheets) {
            stylesheets.forEach((styleSrc)=>{
                _reactdom.default.preinit(styleSrc, {
                    as: 'style'
                });
            });
        }
        // Before interactive scripts need to be loaded by Next.js' runtime instead
        // of native <script> tags, because they no longer have `defer`.
        if (strategy === 'beforeInteractive') {
            if (!src) {
                // For inlined scripts, we put the content in `children`.
                if (restProps.dangerouslySetInnerHTML) {
                    // Casting since lib.dom.d.ts doesn't have TrustedHTML yet.
                    restProps.children = restProps.dangerouslySetInnerHTML.__html;
                    delete restProps.dangerouslySetInnerHTML;
                }
                return /*#__PURE__*/ (0, _jsxruntime.jsx)("script", {
                    nonce: nonce,
                    dangerouslySetInnerHTML: {
                        __html: "(self.__next_s=self.__next_s||[]).push(" + JSON.stringify([
                            0,
                            {
                                ...restProps,
                                id
                            }
                        ]) + ")"
                    }
                });
            } else {
                // @ts-ignore
                _reactdom.default.preload(src, restProps.integrity ? {
                    as: 'script',
                    integrity: restProps.integrity,
                    nonce,
                    crossOrigin: restProps.crossOrigin
                } : {
                    as: 'script',
                    nonce,
                    crossOrigin: restProps.crossOrigin
                });
                return /*#__PURE__*/ (0, _jsxruntime.jsx)("script", {
                    nonce: nonce,
                    dangerouslySetInnerHTML: {
                        __html: "(self.__next_s=self.__next_s||[]).push(" + JSON.stringify([
                            src,
                            {
                                ...restProps,
                                id
                            }
                        ]) + ")"
                    }
                });
            }
        } else if (strategy === 'afterInteractive') {
            if (src) {
                // @ts-ignore
                _reactdom.default.preload(src, restProps.integrity ? {
                    as: 'script',
                    integrity: restProps.integrity,
                    nonce,
                    crossOrigin: restProps.crossOrigin
                } : {
                    as: 'script',
                    nonce,
                    crossOrigin: restProps.crossOrigin
                });
            }
        }
    }
    return null;
}
Object.defineProperty(Script, '__nextScript', {
    value: true
});
const _default = Script;
if ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {
    Object.defineProperty(exports.default, '__esModule', {
        value: true
    });
    Object.assign(exports.default, exports);
    module.exports = exports.default;
} //# sourceMappingURL=script.js.map
}}),
"[project]/node_modules/web-vitals/dist/web-vitals.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "CLSThresholds": (()=>T),
    "FCPThresholds": (()=>b),
    "INPThresholds": (()=>N),
    "LCPThresholds": (()=>x),
    "TTFBThresholds": (()=>$),
    "onCLS": (()=>E),
    "onFCP": (()=>P),
    "onINP": (()=>S),
    "onLCP": (()=>O),
    "onTTFB": (()=>H)
});
let e = -1;
const t = (t)=>{
    addEventListener("pageshow", (n)=>{
        n.persisted && (e = n.timeStamp, t(n));
    }, !0);
}, n = (e, t, n, i)=>{
    let o, s;
    return (r)=>{
        t.value >= 0 && (r || i) && (s = t.value - (o ?? 0), (s || void 0 === o) && (o = t.value, t.delta = s, t.rating = ((e, t)=>e > t[1] ? "poor" : e > t[0] ? "needs-improvement" : "good")(t.value, n), e(t)));
    };
}, i = (e)=>{
    requestAnimationFrame(()=>requestAnimationFrame(()=>e()));
}, o = ()=>{
    const e = performance.getEntriesByType("navigation")[0];
    if (e && e.responseStart > 0 && e.responseStart < performance.now()) return e;
}, s = ()=>{
    const e = o();
    return e?.activationStart ?? 0;
}, r = (t, n = -1)=>{
    const i = o();
    let r = "navigate";
    e >= 0 ? r = "back-forward-cache" : i && (document.prerendering || s() > 0 ? r = "prerender" : document.wasDiscarded ? r = "restore" : i.type && (r = i.type.replace(/_/g, "-")));
    return {
        name: t,
        value: n,
        rating: "good",
        delta: 0,
        entries: [],
        id: `v5-${Date.now()}-${Math.floor(8999999999999 * Math.random()) + 1e12}`,
        navigationType: r
    };
}, c = new WeakMap;
function a(e, t) {
    return c.get(e) || c.set(e, new t), c.get(e);
}
class d {
    t;
    i = 0;
    o = [];
    h(e) {
        if (e.hadRecentInput) return;
        const t = this.o[0], n = this.o.at(-1);
        this.i && t && n && e.startTime - n.startTime < 1e3 && e.startTime - t.startTime < 5e3 ? (this.i += e.value, this.o.push(e)) : (this.i = e.value, this.o = [
            e
        ]), this.t?.(e);
    }
}
const h = (e, t, n = {})=>{
    try {
        if (PerformanceObserver.supportedEntryTypes.includes(e)) {
            const i = new PerformanceObserver((e)=>{
                Promise.resolve().then(()=>{
                    t(e.getEntries());
                });
            });
            return i.observe({
                type: e,
                buffered: !0,
                ...n
            }), i;
        }
    } catch  {}
}, f = (e)=>{
    let t = !1;
    return ()=>{
        t || (e(), t = !0);
    };
};
let u = -1;
const l = ()=>"hidden" !== document.visibilityState || document.prerendering ? 1 / 0 : 0, m = (e)=>{
    "hidden" === document.visibilityState && u > -1 && (u = "visibilitychange" === e.type ? e.timeStamp : 0, v());
}, g = ()=>{
    addEventListener("visibilitychange", m, !0), addEventListener("prerenderingchange", m, !0);
}, v = ()=>{
    removeEventListener("visibilitychange", m, !0), removeEventListener("prerenderingchange", m, !0);
}, p = ()=>{
    if (u < 0) {
        const e = s(), n = document.prerendering ? void 0 : globalThis.performance.getEntriesByType("visibility-state").filter((t)=>"hidden" === t.name && t.startTime > e)[0]?.startTime;
        u = n ?? l(), g(), t(()=>{
            setTimeout(()=>{
                u = l(), g();
            });
        });
    }
    return {
        get firstHiddenTime () {
            return u;
        }
    };
}, y = (e)=>{
    document.prerendering ? addEventListener("prerenderingchange", ()=>e(), !0) : e();
}, b = [
    1800,
    3e3
], P = (e, o = {})=>{
    y(()=>{
        const c = p();
        let a, d = r("FCP");
        const f = h("paint", (e)=>{
            for (const t of e)"first-contentful-paint" === t.name && (f.disconnect(), t.startTime < c.firstHiddenTime && (d.value = Math.max(t.startTime - s(), 0), d.entries.push(t), a(!0)));
        });
        f && (a = n(e, d, b, o.reportAllChanges), t((t)=>{
            d = r("FCP"), a = n(e, d, b, o.reportAllChanges), i(()=>{
                d.value = performance.now() - t.timeStamp, a(!0);
            });
        }));
    });
}, T = [
    .1,
    .25
], E = (e, o = {})=>{
    P(f(()=>{
        let s, c = r("CLS", 0);
        const f = a(o, d), u = (e)=>{
            for (const t of e)f.h(t);
            f.i > c.value && (c.value = f.i, c.entries = f.o, s());
        }, l = h("layout-shift", u);
        l && (s = n(e, c, T, o.reportAllChanges), document.addEventListener("visibilitychange", ()=>{
            "hidden" === document.visibilityState && (u(l.takeRecords()), s(!0));
        }), t(()=>{
            f.i = 0, c = r("CLS", 0), s = n(e, c, T, o.reportAllChanges), i(()=>s());
        }), setTimeout(s));
    }));
};
let _ = 0, L = 1 / 0, M = 0;
const C = (e)=>{
    for (const t of e)t.interactionId && (L = Math.min(L, t.interactionId), M = Math.max(M, t.interactionId), _ = M ? (M - L) / 7 + 1 : 0);
};
let I;
const w = ()=>I ? _ : performance.interactionCount ?? 0, F = ()=>{
    "interactionCount" in performance || I || (I = h("event", C, {
        type: "event",
        buffered: !0,
        durationThreshold: 0
    }));
};
let k = 0;
class A {
    u = [];
    l = new Map;
    m;
    v;
    p() {
        k = w(), this.u.length = 0, this.l.clear();
    }
    P() {
        const e = Math.min(this.u.length - 1, Math.floor((w() - k) / 50));
        return this.u[e];
    }
    h(e) {
        if (this.m?.(e), !e.interactionId && "first-input" !== e.entryType) return;
        const t = this.u.at(-1);
        let n = this.l.get(e.interactionId);
        if (n || this.u.length < 10 || e.duration > t.T) {
            if (n ? e.duration > n.T ? (n.entries = [
                e
            ], n.T = e.duration) : e.duration === n.T && e.startTime === n.entries[0].startTime && n.entries.push(e) : (n = {
                id: e.interactionId,
                entries: [
                    e
                ],
                T: e.duration
            }, this.l.set(n.id, n), this.u.push(n)), this.u.sort((e, t)=>t.T - e.T), this.u.length > 10) {
                const e = this.u.splice(10);
                for (const t of e)this.l.delete(t.id);
            }
            this.v?.(n);
        }
    }
}
const B = (e)=>{
    const t = globalThis.requestIdleCallback || setTimeout;
    "hidden" === document.visibilityState ? e() : (e = f(e), document.addEventListener("visibilitychange", e, {
        once: !0
    }), t(()=>{
        e(), document.removeEventListener("visibilitychange", e);
    }));
}, N = [
    200,
    500
], S = (e, i = {})=>{
    globalThis.PerformanceEventTiming && "interactionId" in PerformanceEventTiming.prototype && y(()=>{
        F();
        let o, s = r("INP");
        const c = a(i, A), d = (e)=>{
            B(()=>{
                for (const t of e)c.h(t);
                const t = c.P();
                t && t.T !== s.value && (s.value = t.T, s.entries = t.entries, o());
            });
        }, f = h("event", d, {
            durationThreshold: i.durationThreshold ?? 40
        });
        o = n(e, s, N, i.reportAllChanges), f && (f.observe({
            type: "first-input",
            buffered: !0
        }), document.addEventListener("visibilitychange", ()=>{
            "hidden" === document.visibilityState && (d(f.takeRecords()), o(!0));
        }), t(()=>{
            c.p(), s = r("INP"), o = n(e, s, N, i.reportAllChanges);
        }));
    });
};
class q {
    m;
    h(e) {
        this.m?.(e);
    }
}
const x = [
    2500,
    4e3
], O = (e, o = {})=>{
    y(()=>{
        const c = p();
        let d, u = r("LCP");
        const l = a(o, q), m = (e)=>{
            o.reportAllChanges || (e = e.slice(-1));
            for (const t of e)l.h(t), t.startTime < c.firstHiddenTime && (u.value = Math.max(t.startTime - s(), 0), u.entries = [
                t
            ], d());
        }, g = h("largest-contentful-paint", m);
        if (g) {
            d = n(e, u, x, o.reportAllChanges);
            const s = f(()=>{
                m(g.takeRecords()), g.disconnect(), d(!0);
            });
            for (const e of [
                "keydown",
                "click",
                "visibilitychange"
            ])addEventListener(e, ()=>B(s), {
                capture: !0,
                once: !0
            });
            t((t)=>{
                u = r("LCP"), d = n(e, u, x, o.reportAllChanges), i(()=>{
                    u.value = performance.now() - t.timeStamp, d(!0);
                });
            });
        }
    });
}, $ = [
    800,
    1800
], D = (e)=>{
    document.prerendering ? y(()=>D(e)) : "complete" !== document.readyState ? addEventListener("load", ()=>D(e), !0) : setTimeout(e);
}, H = (e, i = {})=>{
    let c = r("TTFB"), a = n(e, c, $, i.reportAllChanges);
    D(()=>{
        const d = o();
        d && (c.value = Math.max(d.responseStart - s(), 0), c.entries = [
            d
        ], a(!0), t(()=>{
            c = r("TTFB", 0), a = n(e, c, $, i.reportAllChanges), a(!0);
        }));
    });
};
;
}}),
"[project]/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
/**
 * @license React
 * react-jsx-dev-runtime.development.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */ var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
"use strict";
"production" !== ("TURBOPACK compile-time value", "development") && function() {
    function getComponentNameFromType(type) {
        if (null == type) return null;
        if ("function" === typeof type) return type.$$typeof === REACT_CLIENT_REFERENCE ? null : type.displayName || type.name || null;
        if ("string" === typeof type) return type;
        switch(type){
            case REACT_FRAGMENT_TYPE:
                return "Fragment";
            case REACT_PROFILER_TYPE:
                return "Profiler";
            case REACT_STRICT_MODE_TYPE:
                return "StrictMode";
            case REACT_SUSPENSE_TYPE:
                return "Suspense";
            case REACT_SUSPENSE_LIST_TYPE:
                return "SuspenseList";
            case REACT_ACTIVITY_TYPE:
                return "Activity";
        }
        if ("object" === typeof type) switch("number" === typeof type.tag && console.error("Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue."), type.$$typeof){
            case REACT_PORTAL_TYPE:
                return "Portal";
            case REACT_CONTEXT_TYPE:
                return (type.displayName || "Context") + ".Provider";
            case REACT_CONSUMER_TYPE:
                return (type._context.displayName || "Context") + ".Consumer";
            case REACT_FORWARD_REF_TYPE:
                var innerType = type.render;
                type = type.displayName;
                type || (type = innerType.displayName || innerType.name || "", type = "" !== type ? "ForwardRef(" + type + ")" : "ForwardRef");
                return type;
            case REACT_MEMO_TYPE:
                return innerType = type.displayName || null, null !== innerType ? innerType : getComponentNameFromType(type.type) || "Memo";
            case REACT_LAZY_TYPE:
                innerType = type._payload;
                type = type._init;
                try {
                    return getComponentNameFromType(type(innerType));
                } catch (x) {}
        }
        return null;
    }
    function testStringCoercion(value) {
        return "" + value;
    }
    function checkKeyStringCoercion(value) {
        try {
            testStringCoercion(value);
            var JSCompiler_inline_result = !1;
        } catch (e) {
            JSCompiler_inline_result = !0;
        }
        if (JSCompiler_inline_result) {
            JSCompiler_inline_result = console;
            var JSCompiler_temp_const = JSCompiler_inline_result.error;
            var JSCompiler_inline_result$jscomp$0 = "function" === typeof Symbol && Symbol.toStringTag && value[Symbol.toStringTag] || value.constructor.name || "Object";
            JSCompiler_temp_const.call(JSCompiler_inline_result, "The provided key is an unsupported type %s. This value must be coerced to a string before using it here.", JSCompiler_inline_result$jscomp$0);
            return testStringCoercion(value);
        }
    }
    function getTaskName(type) {
        if (type === REACT_FRAGMENT_TYPE) return "<>";
        if ("object" === typeof type && null !== type && type.$$typeof === REACT_LAZY_TYPE) return "<...>";
        try {
            var name = getComponentNameFromType(type);
            return name ? "<" + name + ">" : "<...>";
        } catch (x) {
            return "<...>";
        }
    }
    function getOwner() {
        var dispatcher = ReactSharedInternals.A;
        return null === dispatcher ? null : dispatcher.getOwner();
    }
    function UnknownOwner() {
        return Error("react-stack-top-frame");
    }
    function hasValidKey(config) {
        if (hasOwnProperty.call(config, "key")) {
            var getter = Object.getOwnPropertyDescriptor(config, "key").get;
            if (getter && getter.isReactWarning) return !1;
        }
        return void 0 !== config.key;
    }
    function defineKeyPropWarningGetter(props, displayName) {
        function warnAboutAccessingKey() {
            specialPropKeyWarningShown || (specialPropKeyWarningShown = !0, console.error("%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)", displayName));
        }
        warnAboutAccessingKey.isReactWarning = !0;
        Object.defineProperty(props, "key", {
            get: warnAboutAccessingKey,
            configurable: !0
        });
    }
    function elementRefGetterWithDeprecationWarning() {
        var componentName = getComponentNameFromType(this.type);
        didWarnAboutElementRef[componentName] || (didWarnAboutElementRef[componentName] = !0, console.error("Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release."));
        componentName = this.props.ref;
        return void 0 !== componentName ? componentName : null;
    }
    function ReactElement(type, key, self, source, owner, props, debugStack, debugTask) {
        self = props.ref;
        type = {
            $$typeof: REACT_ELEMENT_TYPE,
            type: type,
            key: key,
            props: props,
            _owner: owner
        };
        null !== (void 0 !== self ? self : null) ? Object.defineProperty(type, "ref", {
            enumerable: !1,
            get: elementRefGetterWithDeprecationWarning
        }) : Object.defineProperty(type, "ref", {
            enumerable: !1,
            value: null
        });
        type._store = {};
        Object.defineProperty(type._store, "validated", {
            configurable: !1,
            enumerable: !1,
            writable: !0,
            value: 0
        });
        Object.defineProperty(type, "_debugInfo", {
            configurable: !1,
            enumerable: !1,
            writable: !0,
            value: null
        });
        Object.defineProperty(type, "_debugStack", {
            configurable: !1,
            enumerable: !1,
            writable: !0,
            value: debugStack
        });
        Object.defineProperty(type, "_debugTask", {
            configurable: !1,
            enumerable: !1,
            writable: !0,
            value: debugTask
        });
        Object.freeze && (Object.freeze(type.props), Object.freeze(type));
        return type;
    }
    function jsxDEVImpl(type, config, maybeKey, isStaticChildren, source, self, debugStack, debugTask) {
        var children = config.children;
        if (void 0 !== children) if (isStaticChildren) if (isArrayImpl(children)) {
            for(isStaticChildren = 0; isStaticChildren < children.length; isStaticChildren++)validateChildKeys(children[isStaticChildren]);
            Object.freeze && Object.freeze(children);
        } else console.error("React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.");
        else validateChildKeys(children);
        if (hasOwnProperty.call(config, "key")) {
            children = getComponentNameFromType(type);
            var keys = Object.keys(config).filter(function(k) {
                return "key" !== k;
            });
            isStaticChildren = 0 < keys.length ? "{key: someKey, " + keys.join(": ..., ") + ": ...}" : "{key: someKey}";
            didWarnAboutKeySpread[children + isStaticChildren] || (keys = 0 < keys.length ? "{" + keys.join(": ..., ") + ": ...}" : "{}", console.error('A props object containing a "key" prop is being spread into JSX:\n  let props = %s;\n  <%s {...props} />\nReact keys must be passed directly to JSX without using spread:\n  let props = %s;\n  <%s key={someKey} {...props} />', isStaticChildren, children, keys, children), didWarnAboutKeySpread[children + isStaticChildren] = !0);
        }
        children = null;
        void 0 !== maybeKey && (checkKeyStringCoercion(maybeKey), children = "" + maybeKey);
        hasValidKey(config) && (checkKeyStringCoercion(config.key), children = "" + config.key);
        if ("key" in config) {
            maybeKey = {};
            for(var propName in config)"key" !== propName && (maybeKey[propName] = config[propName]);
        } else maybeKey = config;
        children && defineKeyPropWarningGetter(maybeKey, "function" === typeof type ? type.displayName || type.name || "Unknown" : type);
        return ReactElement(type, children, self, source, getOwner(), maybeKey, debugStack, debugTask);
    }
    function validateChildKeys(node) {
        "object" === typeof node && null !== node && node.$$typeof === REACT_ELEMENT_TYPE && node._store && (node._store.validated = 1);
    }
    var React = __turbopack_context__.r("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)"), REACT_ELEMENT_TYPE = Symbol.for("react.transitional.element"), REACT_PORTAL_TYPE = Symbol.for("react.portal"), REACT_FRAGMENT_TYPE = Symbol.for("react.fragment"), REACT_STRICT_MODE_TYPE = Symbol.for("react.strict_mode"), REACT_PROFILER_TYPE = Symbol.for("react.profiler");
    Symbol.for("react.provider");
    var REACT_CONSUMER_TYPE = Symbol.for("react.consumer"), REACT_CONTEXT_TYPE = Symbol.for("react.context"), REACT_FORWARD_REF_TYPE = Symbol.for("react.forward_ref"), REACT_SUSPENSE_TYPE = Symbol.for("react.suspense"), REACT_SUSPENSE_LIST_TYPE = Symbol.for("react.suspense_list"), REACT_MEMO_TYPE = Symbol.for("react.memo"), REACT_LAZY_TYPE = Symbol.for("react.lazy"), REACT_ACTIVITY_TYPE = Symbol.for("react.activity"), REACT_CLIENT_REFERENCE = Symbol.for("react.client.reference"), ReactSharedInternals = React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE, hasOwnProperty = Object.prototype.hasOwnProperty, isArrayImpl = Array.isArray, createTask = console.createTask ? console.createTask : function() {
        return null;
    };
    React = {
        "react-stack-bottom-frame": function(callStackForError) {
            return callStackForError();
        }
    };
    var specialPropKeyWarningShown;
    var didWarnAboutElementRef = {};
    var unknownOwnerDebugStack = React["react-stack-bottom-frame"].bind(React, UnknownOwner)();
    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));
    var didWarnAboutKeySpread = {};
    exports.Fragment = REACT_FRAGMENT_TYPE;
    exports.jsxDEV = function(type, config, maybeKey, isStaticChildren, source, self) {
        var trackActualOwner = 1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;
        return jsxDEVImpl(type, config, maybeKey, isStaticChildren, source, self, trackActualOwner ? Error("react-stack-top-frame") : unknownOwnerDebugStack, trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask);
    };
}();
}}),
"[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
'use strict';
if ("TURBOPACK compile-time falsy", 0) {
    "TURBOPACK unreachable";
} else {
    module.exports = __turbopack_context__.r("[project]/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js [app-client] (ecmascript)");
}
}}),
}]);

//# sourceMappingURL=_8ef35db7._.js.map