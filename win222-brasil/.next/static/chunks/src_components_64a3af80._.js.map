{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/%E6%96%87%E6%A1%A3/win222/win222-brasil/src/components/OptimizedImage.tsx"], "sourcesContent": ["'use client';\n\nimport Image from 'next/image';\nimport { useState, useRef, useEffect } from 'react';\n\ninterface OptimizedImageProps {\n  src: string;\n  alt: string;\n  width?: number;\n  height?: number;\n  className?: string;\n  priority?: boolean;\n  quality?: number;\n  placeholder?: 'blur' | 'empty';\n  blurDataURL?: string;\n  sizes?: string;\n  fill?: boolean;\n  loading?: 'lazy' | 'eager';\n  onLoad?: () => void;\n  onError?: () => void;\n}\n\nexport default function OptimizedImage({\n  src,\n  alt,\n  width,\n  height,\n  className = '',\n  priority = false,\n  quality = 85,\n  placeholder = 'blur',\n  blurDataURL,\n  sizes,\n  fill = false,\n  loading = 'lazy',\n  onLoad,\n  onError,\n}: OptimizedImageProps) {\n  const [isLoaded, setIsLoaded] = useState(false);\n  const [hasError, setHasError] = useState(false);\n  const [isInView, setIsInView] = useState(priority);\n  const imgRef = useRef<HTMLDivElement>(null);\n\n  // Intersection Observer para lazy loading\n  useEffect(() => {\n    if (priority || isInView) return;\n\n    const observer = new IntersectionObserver(\n      (entries) => {\n        entries.forEach((entry) => {\n          if (entry.isIntersecting) {\n            setIsInView(true);\n            observer.disconnect();\n          }\n        });\n      },\n      {\n        rootMargin: '50px', // Carregar 50px antes de entrar na viewport\n        threshold: 0.1,\n      }\n    );\n\n    if (imgRef.current) {\n      observer.observe(imgRef.current);\n    }\n\n    return () => observer.disconnect();\n  }, [priority, isInView]);\n\n  // Gerar placeholder blur automático se não fornecido\n  const generateBlurDataURL = (w: number = 10, h: number = 10) => {\n    if (typeof document === 'undefined') {\n      // Fallback para SSR\n      return 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAiIGhlaWdodD0iMTAiIHZpZXdCb3g9IjAgMCAxMCAxMCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjEwIiBoZWlnaHQ9IjEwIiBmaWxsPSIjZjNmNGY2Ii8+Cjwvc3ZnPgo=';\n    }\n\n    const canvas = document.createElement('canvas');\n    canvas.width = w;\n    canvas.height = h;\n    const ctx = canvas.getContext('2d');\n\n    if (ctx) {\n      // Gradiente simples para placeholder\n      const gradient = ctx.createLinearGradient(0, 0, w, h);\n      gradient.addColorStop(0, '#f3f4f6');\n      gradient.addColorStop(1, '#e5e7eb');\n      ctx.fillStyle = gradient;\n      ctx.fillRect(0, 0, w, h);\n    }\n\n    return canvas.toDataURL();\n  };\n\n  const handleLoad = () => {\n    setIsLoaded(true);\n    onLoad?.();\n  };\n\n  const handleError = () => {\n    setHasError(true);\n    onError?.();\n  };\n\n  // Placeholder enquanto não carrega\n  if (!isInView && !priority) {\n    return (\n      <div\n        ref={imgRef}\n        className={`bg-gray-200 animate-pulse ${className}`}\n        style={{\n          width: fill ? '100%' : width,\n          height: fill ? '100%' : height,\n          aspectRatio: width && height ? `${width}/${height}` : undefined,\n        }}\n        aria-label={`Carregando: ${alt}`}\n      />\n    );\n  }\n\n  // Imagem de erro\n  if (hasError) {\n    return (\n      <div\n        className={`bg-gray-100 flex items-center justify-center text-gray-400 ${className}`}\n        style={{\n          width: fill ? '100%' : width,\n          height: fill ? '100%' : height,\n          aspectRatio: width && height ? `${width}/${height}` : undefined,\n        }}\n      >\n        <svg\n          className=\"w-8 h-8\"\n          fill=\"none\"\n          stroke=\"currentColor\"\n          viewBox=\"0 0 24 24\"\n        >\n          <path\n            strokeLinecap=\"round\"\n            strokeLinejoin=\"round\"\n            strokeWidth={2}\n            d=\"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\"\n          />\n        </svg>\n      </div>\n    );\n  }\n\n  const imageProps = {\n    src,\n    alt,\n    quality,\n    priority,\n    loading: priority ? 'eager' as const : loading,\n    onLoad: handleLoad,\n    onError: handleError,\n    className: `transition-opacity duration-300 ${\n      isLoaded ? 'opacity-100' : 'opacity-0'\n    } ${className}`,\n    placeholder: placeholder as 'blur' | 'empty',\n    blurDataURL: blurDataURL || (placeholder === 'blur' ? generateBlurDataURL() : undefined),\n    sizes: sizes || '(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw',\n  };\n\n  if (fill) {\n    return (\n      <div ref={imgRef} className=\"relative overflow-hidden\">\n        <Image\n          {...imageProps}\n          fill\n          style={{ objectFit: 'cover' }}\n        />\n      </div>\n    );\n  }\n\n  return (\n    <div ref={imgRef}>\n      <Image\n        {...imageProps}\n        width={width}\n        height={height}\n      />\n    </div>\n  );\n}\n\n// Componente para hero images com otimizações específicas\nexport function HeroImage({\n  src,\n  alt,\n  className = '',\n  ...props\n}: Omit<OptimizedImageProps, 'priority' | 'loading'>) {\n  return (\n    <OptimizedImage\n      src={src}\n      alt={alt}\n      className={className}\n      priority={true}\n      loading=\"eager\"\n      quality={90}\n      sizes=\"100vw\"\n      {...props}\n    />\n  );\n}\n\n// Componente para thumbnails com otimizações específicas\nexport function ThumbnailImage({\n  src,\n  alt,\n  size = 150,\n  className = '',\n  ...props\n}: Omit<OptimizedImageProps, 'width' | 'height'> & { size?: number }) {\n  return (\n    <OptimizedImage\n      src={src}\n      alt={alt}\n      width={size}\n      height={size}\n      className={`rounded-lg ${className}`}\n      quality={75}\n      sizes=\"(max-width: 768px) 150px, 200px\"\n      {...props}\n    />\n  );\n}\n\n// Componente para logos com otimizações específicas\nexport function LogoImage({\n  src,\n  alt,\n  width = 120,\n  height = 40,\n  className = '',\n  ...props\n}: Omit<OptimizedImageProps, 'placeholder'>) {\n  return (\n    <OptimizedImage\n      src={src}\n      alt={alt}\n      width={width}\n      height={height}\n      className={className}\n      priority={true}\n      quality={95}\n      placeholder=\"empty\"\n      sizes=\"(max-width: 768px) 100px, 120px\"\n      {...props}\n    />\n  );\n}\n\n// Hook para preload de imagens críticas\nexport function useImagePreload(urls: string[]) {\n  useEffect(() => {\n    urls.forEach((url) => {\n      const link = document.createElement('link');\n      link.rel = 'preload';\n      link.as = 'image';\n      link.href = url;\n      document.head.appendChild(link);\n    });\n\n    // Cleanup\n    return () => {\n      urls.forEach((url) => {\n        const links = document.querySelectorAll(`link[href=\"${url}\"]`);\n        links.forEach((link) => link.remove());\n      });\n    };\n  }, [urls]);\n}\n"], "names": [], "mappings": ";;;;;;;;AAEA;AACA;;;AAHA;;;AAsBe,SAAS,eAAe,EACrC,GAAG,EACH,GAAG,EACH,KAAK,EACL,MAAM,EACN,YAAY,EAAE,EACd,WAAW,KAAK,EAChB,UAAU,EAAE,EACZ,cAAc,MAAM,EACpB,WAAW,EACX,KAAK,EACL,OAAO,KAAK,EACZ,UAAU,MAAM,EAChB,MAAM,EACN,OAAO,EACa;;IACpB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,SAAS,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAEtC,0CAA0C;IAC1C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,IAAI,YAAY,UAAU;YAE1B,MAAM,WAAW,IAAI;4CACnB,CAAC;oBACC,QAAQ,OAAO;oDAAC,CAAC;4BACf,IAAI,MAAM,cAAc,EAAE;gCACxB,YAAY;gCACZ,SAAS,UAAU;4BACrB;wBACF;;gBACF;2CACA;gBACE,YAAY;gBACZ,WAAW;YACb;YAGF,IAAI,OAAO,OAAO,EAAE;gBAClB,SAAS,OAAO,CAAC,OAAO,OAAO;YACjC;YAEA;4CAAO,IAAM,SAAS,UAAU;;QAClC;mCAAG;QAAC;QAAU;KAAS;IAEvB,qDAAqD;IACrD,MAAM,sBAAsB,CAAC,IAAY,EAAE,EAAE,IAAY,EAAE;QACzD,IAAI,OAAO,aAAa,aAAa;YACnC,oBAAoB;YACpB,OAAO;QACT;QAEA,MAAM,SAAS,SAAS,aAAa,CAAC;QACtC,OAAO,KAAK,GAAG;QACf,OAAO,MAAM,GAAG;QAChB,MAAM,MAAM,OAAO,UAAU,CAAC;QAE9B,IAAI,KAAK;YACP,qCAAqC;YACrC,MAAM,WAAW,IAAI,oBAAoB,CAAC,GAAG,GAAG,GAAG;YACnD,SAAS,YAAY,CAAC,GAAG;YACzB,SAAS,YAAY,CAAC,GAAG;YACzB,IAAI,SAAS,GAAG;YAChB,IAAI,QAAQ,CAAC,GAAG,GAAG,GAAG;QACxB;QAEA,OAAO,OAAO,SAAS;IACzB;IAEA,MAAM,aAAa;QACjB,YAAY;QACZ;IACF;IAEA,MAAM,cAAc;QAClB,YAAY;QACZ;IACF;IAEA,mCAAmC;IACnC,IAAI,CAAC,YAAY,CAAC,UAAU;QAC1B,qBACE,6LAAC;YACC,KAAK;YACL,WAAW,CAAC,0BAA0B,EAAE,WAAW;YACnD,OAAO;gBACL,OAAO,OAAO,SAAS;gBACvB,QAAQ,OAAO,SAAS;gBACxB,aAAa,SAAS,SAAS,GAAG,MAAM,CAAC,EAAE,QAAQ,GAAG;YACxD;YACA,cAAY,CAAC,YAAY,EAAE,KAAK;;;;;;IAGtC;IAEA,iBAAiB;IACjB,IAAI,UAAU;QACZ,qBACE,6LAAC;YACC,WAAW,CAAC,2DAA2D,EAAE,WAAW;YACpF,OAAO;gBACL,OAAO,OAAO,SAAS;gBACvB,QAAQ,OAAO,SAAS;gBACxB,aAAa,SAAS,SAAS,GAAG,MAAM,CAAC,EAAE,QAAQ,GAAG;YACxD;sBAEA,cAAA,6LAAC;gBACC,WAAU;gBACV,MAAK;gBACL,QAAO;gBACP,SAAQ;0BAER,cAAA,6LAAC;oBACC,eAAc;oBACd,gBAAe;oBACf,aAAa;oBACb,GAAE;;;;;;;;;;;;;;;;IAKZ;IAEA,MAAM,aAAa;QACjB;QACA;QACA;QACA;QACA,SAAS,WAAW,UAAmB;QACvC,QAAQ;QACR,SAAS;QACT,WAAW,CAAC,gCAAgC,EAC1C,WAAW,gBAAgB,YAC5B,CAAC,EAAE,WAAW;QACf,aAAa;QACb,aAAa,eAAe,CAAC,gBAAgB,SAAS,wBAAwB,SAAS;QACvF,OAAO,SAAS;IAClB;IAEA,IAAI,MAAM;QACR,qBACE,6LAAC;YAAI,KAAK;YAAQ,WAAU;sBAC1B,cAAA,6LAAC,gIAAA,CAAA,UAAK;gBACH,GAAG,UAAU;gBACd,IAAI;gBACJ,OAAO;oBAAE,WAAW;gBAAQ;;;;;;;;;;;IAIpC;IAEA,qBACE,6LAAC;QAAI,KAAK;kBACR,cAAA,6LAAC,gIAAA,CAAA,UAAK;YACH,GAAG,UAAU;YACd,OAAO;YACP,QAAQ;;;;;;;;;;;AAIhB;GAlKwB;KAAA;AAqKjB,SAAS,UAAU,EACxB,GAAG,EACH,GAAG,EACH,YAAY,EAAE,EACd,GAAG,OAC+C;IAClD,qBACE,6LAAC;QACC,KAAK;QACL,KAAK;QACL,WAAW;QACX,UAAU;QACV,SAAQ;QACR,SAAS;QACT,OAAM;QACL,GAAG,KAAK;;;;;;AAGf;MAlBgB;AAqBT,SAAS,eAAe,EAC7B,GAAG,EACH,GAAG,EACH,OAAO,GAAG,EACV,YAAY,EAAE,EACd,GAAG,OAC+D;IAClE,qBACE,6LAAC;QACC,KAAK;QACL,KAAK;QACL,OAAO;QACP,QAAQ;QACR,WAAW,CAAC,WAAW,EAAE,WAAW;QACpC,SAAS;QACT,OAAM;QACL,GAAG,KAAK;;;;;;AAGf;MAnBgB;AAsBT,SAAS,UAAU,EACxB,GAAG,EACH,GAAG,EACH,QAAQ,GAAG,EACX,SAAS,EAAE,EACX,YAAY,EAAE,EACd,GAAG,OACsC;IACzC,qBACE,6LAAC;QACC,KAAK;QACL,KAAK;QACL,OAAO;QACP,QAAQ;QACR,WAAW;QACX,UAAU;QACV,SAAS;QACT,aAAY;QACZ,OAAM;QACL,GAAG,KAAK;;;;;;AAGf;MAtBgB;AAyBT,SAAS,gBAAgB,IAAc;;IAC5C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,KAAK,OAAO;6CAAC,CAAC;oBACZ,MAAM,OAAO,SAAS,aAAa,CAAC;oBACpC,KAAK,GAAG,GAAG;oBACX,KAAK,EAAE,GAAG;oBACV,KAAK,IAAI,GAAG;oBACZ,SAAS,IAAI,CAAC,WAAW,CAAC;gBAC5B;;YAEA,UAAU;YACV;6CAAO;oBACL,KAAK,OAAO;qDAAC,CAAC;4BACZ,MAAM,QAAQ,SAAS,gBAAgB,CAAC,CAAC,WAAW,EAAE,IAAI,EAAE,CAAC;4BAC7D,MAAM,OAAO;6DAAC,CAAC,OAAS,KAAK,MAAM;;wBACrC;;gBACF;;QACF;oCAAG;QAAC;KAAK;AACX;IAlBgB", "debugId": null}}, {"offset": {"line": 290, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/%E6%96%87%E6%A1%A3/win222/win222-brasil/src/components/MobileNavigation.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport Link from 'next/link';\nimport { usePathname } from 'next/navigation';\nimport { LogoImage } from './OptimizedImage';\n\ninterface NavigationItem {\n  name: string;\n  href: string;\n  icon?: React.ReactNode;\n  badge?: string;\n}\n\nconst navigationItems: NavigationItem[] = [\n  {\n    name: '<PERSON><PERSON><PERSON>',\n    href: '/',\n    icon: (\n      <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6\" />\n      </svg>\n    ),\n  },\n  {\n    name: '<PERSON><PERSON>',\n    href: '/jogos',\n    icon: (\n      <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h.01M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16l3.5-2 3.5 2 3.5-2 3.5 2z\" />\n      </svg>\n    ),\n    badge: '1000+',\n  },\n  {\n    name: 'Cassino',\n    href: '/cassino',\n    icon: (\n      <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\n      </svg>\n    ),\n  },\n  {\n    name: 'Apostas',\n    href: '/apostas',\n    icon: (\n      <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 10V3L4 14h7v7l9-11h-7z\" />\n      </svg>\n    ),\n  },\n  {\n    name: 'Promoções',\n    href: '/promocoes',\n    icon: (\n      <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8v13m0-13V6a2 2 0 112 2h-2zm0 0V5.5A2.5 2.5 0 109.5 8H12zm-7 4h14M5 12a2 2 0 110-4h14a2 2 0 110 4M5 12v7a2 2 0 002 2h10a2 2 0 002-2v-7\" />\n      </svg>\n    ),\n    badge: 'NOVO',\n  },\n];\n\nexport default function MobileNavigation() {\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const [isScrolled, setIsScrolled] = useState(false);\n  const pathname = usePathname();\n\n  // Detectar scroll para mudar estilo do header\n  useEffect(() => {\n    const handleScroll = () => {\n      setIsScrolled(window.scrollY > 10);\n    };\n\n    window.addEventListener('scroll', handleScroll, { passive: true });\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  // Fechar menu ao mudar de página\n  useEffect(() => {\n    setIsMenuOpen(false);\n  }, [pathname]);\n\n  // Prevenir scroll do body quando menu está aberto\n  useEffect(() => {\n    if (isMenuOpen) {\n      document.body.style.overflow = 'hidden';\n    } else {\n      document.body.style.overflow = 'unset';\n    }\n\n    return () => {\n      document.body.style.overflow = 'unset';\n    };\n  }, [isMenuOpen]);\n\n  return (\n    <>\n      {/* Header fixo */}\n      <header\n        className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${\n          isScrolled\n            ? 'bg-gray-900/95 backdrop-blur-md shadow-lg'\n            : 'bg-transparent'\n        }`}\n      >\n        <div className=\"flex items-center justify-between px-4 py-3\">\n          {/* Logo */}\n          <Link href=\"/\" className=\"flex items-center space-x-2\">\n            <LogoImage\n              src=\"/images/logo.png\"\n              alt=\"Win222 Brasil\"\n              width={40}\n              height={40}\n              className=\"rounded-lg\"\n            />\n            <span className=\"text-xl font-bold text-white\">\n              Win222\n            </span>\n          </Link>\n\n          {/* Botões de ação (desktop) */}\n          <div className=\"hidden md:flex items-center space-x-4\">\n            <button className=\"bg-yellow-500 hover:bg-yellow-600 text-black px-6 py-2 rounded-lg font-semibold transition-colors\">\n              Cadastrar\n            </button>\n            <button className=\"border border-yellow-500 text-yellow-500 hover:bg-yellow-500 hover:text-black px-6 py-2 rounded-lg font-semibold transition-colors\">\n              Entrar\n            </button>\n          </div>\n\n          {/* Menu hamburger (mobile) */}\n          <button\n            onClick={() => setIsMenuOpen(!isMenuOpen)}\n            className=\"md:hidden p-2 text-white\"\n            aria-label=\"Menu\"\n          >\n            <svg\n              className={`w-6 h-6 transition-transform duration-300 ${\n                isMenuOpen ? 'rotate-90' : ''\n              }`}\n              fill=\"none\"\n              stroke=\"currentColor\"\n              viewBox=\"0 0 24 24\"\n            >\n              {isMenuOpen ? (\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  strokeWidth={2}\n                  d=\"M6 18L18 6M6 6l12 12\"\n                />\n              ) : (\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  strokeWidth={2}\n                  d=\"M4 6h16M4 12h16M4 18h16\"\n                />\n              )}\n            </svg>\n          </button>\n        </div>\n\n        {/* Navegação desktop */}\n        <nav className=\"hidden md:block border-t border-gray-700\">\n          <div className=\"flex items-center justify-center space-x-8 px-4 py-3\">\n            {navigationItems.map((item) => (\n              <Link\n                key={item.href}\n                href={item.href}\n                className={`flex items-center space-x-2 px-3 py-2 rounded-lg transition-colors ${\n                  pathname === item.href\n                    ? 'bg-yellow-500 text-black'\n                    : 'text-white hover:bg-gray-700'\n                }`}\n              >\n                {item.icon}\n                <span className=\"font-medium\">{item.name}</span>\n                {item.badge && (\n                  <span className=\"bg-red-500 text-white text-xs px-2 py-1 rounded-full\">\n                    {item.badge}\n                  </span>\n                )}\n              </Link>\n            ))}\n          </div>\n        </nav>\n      </header>\n\n      {/* Menu mobile overlay */}\n      {isMenuOpen && (\n        <div className=\"fixed inset-0 z-40 md:hidden\">\n          {/* Backdrop */}\n          <div\n            className=\"absolute inset-0 bg-black/50 backdrop-blur-sm\"\n            onClick={() => setIsMenuOpen(false)}\n          />\n\n          {/* Menu content */}\n          <div className=\"absolute top-0 right-0 w-80 max-w-[90vw] h-full bg-gray-900 shadow-xl\">\n            <div className=\"flex flex-col h-full\">\n              {/* Header do menu */}\n              <div className=\"flex items-center justify-between p-4 border-b border-gray-700\">\n                <div className=\"flex items-center space-x-2\">\n                  <LogoImage\n                    src=\"/images/logo.png\"\n                    alt=\"Win222 Brasil\"\n                    width={32}\n                    height={32}\n                    className=\"rounded-lg\"\n                  />\n                  <span className=\"text-lg font-bold text-white\">\n                    Win222 Brasil\n                  </span>\n                </div>\n                <button\n                  onClick={() => setIsMenuOpen(false)}\n                  className=\"p-2 text-gray-400 hover:text-white\"\n                >\n                  <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n                  </svg>\n                </button>\n              </div>\n\n              {/* Navegação */}\n              <nav className=\"flex-1 p-4\">\n                <div className=\"space-y-2\">\n                  {navigationItems.map((item) => (\n                    <Link\n                      key={item.href}\n                      href={item.href}\n                      className={`flex items-center justify-between p-3 rounded-lg transition-colors ${\n                        pathname === item.href\n                          ? 'bg-yellow-500 text-black'\n                          : 'text-white hover:bg-gray-800'\n                      }`}\n                    >\n                      <div className=\"flex items-center space-x-3\">\n                        {item.icon}\n                        <span className=\"font-medium\">{item.name}</span>\n                      </div>\n                      {item.badge && (\n                        <span className=\"bg-red-500 text-white text-xs px-2 py-1 rounded-full\">\n                          {item.badge}\n                        </span>\n                      )}\n                    </Link>\n                  ))}\n                </div>\n              </nav>\n\n              {/* Botões de ação */}\n              <div className=\"p-4 border-t border-gray-700 space-y-3\">\n                <button className=\"w-full bg-yellow-500 hover:bg-yellow-600 text-black py-3 rounded-lg font-semibold transition-colors\">\n                  Cadastrar Grátis\n                </button>\n                <button className=\"w-full border border-yellow-500 text-yellow-500 hover:bg-yellow-500 hover:text-black py-3 rounded-lg font-semibold transition-colors\">\n                  Fazer Login\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Spacer para compensar header fixo */}\n      <div className=\"h-16 md:h-24\" />\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAcA,MAAM,kBAAoC;IACxC;QACE,MAAM;QACN,MAAM;QACN,oBACE,6LAAC;YAAI,WAAU;YAAU,MAAK;YAAO,QAAO;YAAe,SAAQ;sBACjE,cAAA,6LAAC;gBAAK,eAAc;gBAAQ,gBAAe;gBAAQ,aAAa;gBAAG,GAAE;;;;;;;;;;;IAG3E;IACA;QACE,MAAM;QACN,MAAM;QACN,oBACE,6LAAC;YAAI,WAAU;YAAU,MAAK;YAAO,QAAO;YAAe,SAAQ;sBACjE,cAAA,6LAAC;gBAAK,eAAc;gBAAQ,gBAAe;gBAAQ,aAAa;gBAAG,GAAE;;;;;;;;;;;QAGzE,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,oBACE,6LAAC;YAAI,WAAU;YAAU,MAAK;YAAO,QAAO;YAAe,SAAQ;sBACjE,cAAA,6LAAC;gBAAK,eAAc;gBAAQ,gBAAe;gBAAQ,aAAa;gBAAG,GAAE;;;;;;;;;;;IAG3E;IACA;QACE,MAAM;QACN,MAAM;QACN,oBACE,6LAAC;YAAI,WAAU;YAAU,MAAK;YAAO,QAAO;YAAe,SAAQ;sBACjE,cAAA,6LAAC;gBAAK,eAAc;gBAAQ,gBAAe;gBAAQ,aAAa;gBAAG,GAAE;;;;;;;;;;;IAG3E;IACA;QACE,MAAM;QACN,MAAM;QACN,oBACE,6LAAC;YAAI,WAAU;YAAU,MAAK;YAAO,QAAO;YAAe,SAAQ;sBACjE,cAAA,6LAAC;gBAAK,eAAc;gBAAQ,gBAAe;gBAAQ,aAAa;gBAAG,GAAE;;;;;;;;;;;QAGzE,OAAO;IACT;CACD;AAEc,SAAS;;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,8CAA8C;IAC9C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,MAAM;2DAAe;oBACnB,cAAc,OAAO,OAAO,GAAG;gBACjC;;YAEA,OAAO,gBAAgB,CAAC,UAAU,cAAc;gBAAE,SAAS;YAAK;YAChE;8CAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;qCAAG,EAAE;IAEL,iCAAiC;IACjC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,cAAc;QAChB;qCAAG;QAAC;KAAS;IAEb,kDAAkD;IAClD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,IAAI,YAAY;gBACd,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;YACjC,OAAO;gBACL,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;YACjC;YAEA;8CAAO;oBACL,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;gBACjC;;QACF;qCAAG;QAAC;KAAW;IAEf,qBACE;;0BAEE,6LAAC;gBACC,WAAW,CAAC,4DAA4D,EACtE,aACI,8CACA,kBACJ;;kCAEF,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;;kDACvB,6LAAC,uIAAA,CAAA,YAAS;wCACR,KAAI;wCACJ,KAAI;wCACJ,OAAO;wCACP,QAAQ;wCACR,WAAU;;;;;;kDAEZ,6LAAC;wCAAK,WAAU;kDAA+B;;;;;;;;;;;;0CAMjD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAO,WAAU;kDAAoG;;;;;;kDAGtH,6LAAC;wCAAO,WAAU;kDAAqI;;;;;;;;;;;;0CAMzJ,6LAAC;gCACC,SAAS,IAAM,cAAc,CAAC;gCAC9B,WAAU;gCACV,cAAW;0CAEX,cAAA,6LAAC;oCACC,WAAW,CAAC,0CAA0C,EACpD,aAAa,cAAc,IAC3B;oCACF,MAAK;oCACL,QAAO;oCACP,SAAQ;8CAEP,2BACC,6LAAC;wCACC,eAAc;wCACd,gBAAe;wCACf,aAAa;wCACb,GAAE;;;;;6DAGJ,6LAAC;wCACC,eAAc;wCACd,gBAAe;wCACf,aAAa;wCACb,GAAE;;;;;;;;;;;;;;;;;;;;;;kCAQZ,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACZ,gBAAgB,GAAG,CAAC,CAAC,qBACpB,6LAAC,+JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAW,CAAC,mEAAmE,EAC7E,aAAa,KAAK,IAAI,GAClB,6BACA,gCACJ;;wCAED,KAAK,IAAI;sDACV,6LAAC;4CAAK,WAAU;sDAAe,KAAK,IAAI;;;;;;wCACvC,KAAK,KAAK,kBACT,6LAAC;4CAAK,WAAU;sDACb,KAAK,KAAK;;;;;;;mCAZV,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;YAsBvB,4BACC,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBACC,WAAU;wBACV,SAAS,IAAM,cAAc;;;;;;kCAI/B,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,uIAAA,CAAA,YAAS;oDACR,KAAI;oDACJ,KAAI;oDACJ,OAAO;oDACP,QAAQ;oDACR,WAAU;;;;;;8DAEZ,6LAAC;oDAAK,WAAU;8DAA+B;;;;;;;;;;;;sDAIjD,6LAAC;4CACC,SAAS,IAAM,cAAc;4CAC7B,WAAU;sDAEV,cAAA,6LAAC;gDAAI,WAAU;gDAAU,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DACjE,cAAA,6LAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;8CAM3E,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;kDACZ,gBAAgB,GAAG,CAAC,CAAC,qBACpB,6LAAC,+JAAA,CAAA,UAAI;gDAEH,MAAM,KAAK,IAAI;gDACf,WAAW,CAAC,mEAAmE,EAC7E,aAAa,KAAK,IAAI,GAClB,6BACA,gCACJ;;kEAEF,6LAAC;wDAAI,WAAU;;4DACZ,KAAK,IAAI;0EACV,6LAAC;gEAAK,WAAU;0EAAe,KAAK,IAAI;;;;;;;;;;;;oDAEzC,KAAK,KAAK,kBACT,6LAAC;wDAAK,WAAU;kEACb,KAAK,KAAK;;;;;;;+CAdV,KAAK,IAAI;;;;;;;;;;;;;;;8CAuBtB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAO,WAAU;sDAAsG;;;;;;sDAGxH,6LAAC;4CAAO,WAAU;sDAAuI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUnK,6LAAC;gBAAI,WAAU;;;;;;;;AAGrB;GAhNwB;;QAGL,qIAAA,CAAA,cAAW;;;KAHN", "debugId": null}}, {"offset": {"line": 828, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/%E6%96%87%E6%A1%A3/win222/win222-brasil/src/components/GameCard.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Link from 'next/link';\nimport { ThumbnailImage } from './OptimizedImage';\nimport { trackConversion, generateAffiliateUrl, getPrimaryAffiliate } from '@/lib/affiliate';\n\ninterface GameCardProps {\n  id: string;\n  title: string;\n  image: string;\n  category: string;\n  rating?: number;\n  isNew?: boolean;\n  isHot?: boolean;\n  provider?: string;\n  description?: string;\n  href?: string;\n  onPlay?: () => void;\n  onFavorite?: () => void;\n  isFavorite?: boolean;\n  affiliateCategory?: 'casino' | 'sports' | 'poker' | 'slots' | 'live';\n  enableAffiliate?: boolean;\n}\n\nexport default function GameCard({\n  id,\n  title,\n  image,\n  category,\n  rating = 0,\n  isNew = false,\n  isHot = false,\n  provider,\n  description,\n  href,\n  onPlay,\n  onFavorite,\n  isFavorite = false,\n  affiliateCategory,\n  enableAffiliate = true,\n}: GameCardProps) {\n  const [isHovered, setIsHovered] = useState(false);\n  const [imageError, setImageError] = useState(false);\n\n  // Get affiliate link based on category\n  const getAffiliateCategory = () => {\n    if (affiliateCategory) return affiliateCategory;\n\n    // Map game categories to affiliate categories\n    const categoryMap: Record<string, 'casino' | 'sports' | 'poker' | 'slots' | 'live'> = {\n      'Slots': 'slots',\n      'Mesa': 'casino',\n      'Crash': 'casino',\n      'Original': 'casino',\n      'Ao Vivo': 'live',\n      'Esportes': 'sports',\n      'Poker': 'poker'\n    };\n\n    return categoryMap[category] || 'casino';\n  };\n\n  const handlePlay = (e: React.MouseEvent) => {\n    e.preventDefault();\n\n    if (enableAffiliate) {\n      // Track conversion and redirect to affiliate link\n      const affCategory = getAffiliateCategory();\n      const affiliate = getPrimaryAffiliate(affCategory);\n\n      if (affiliate) {\n        trackConversion({\n          type: 'play',\n          affiliateId: affiliate.id,\n          metadata: {\n            game_id: id,\n            game_title: title,\n            game_category: category,\n            provider: provider\n          }\n        });\n\n        const affiliateUrl = generateAffiliateUrl(affiliate.id, {\n          game: id,\n          category: category,\n          source: 'game_card'\n        });\n\n        window.open(affiliateUrl, '_blank', 'noopener,noreferrer');\n        return;\n      }\n    }\n\n    onPlay?.();\n  };\n\n  const handleFavorite = (e: React.MouseEvent) => {\n    e.preventDefault();\n    e.stopPropagation();\n    onFavorite?.();\n  };\n\n  const CardContent = () => (\n    <div\n      className={`group relative bg-gray-800 rounded-xl overflow-hidden shadow-lg transition-all duration-300 hover:shadow-2xl hover:scale-105 ${\n        isHovered ? 'ring-2 ring-yellow-500' : ''\n      }`}\n      onMouseEnter={() => setIsHovered(true)}\n      onMouseLeave={() => setIsHovered(false)}\n    >\n      {/* Imagem do jogo */}\n      <div className=\"relative aspect-[4/3] overflow-hidden\">\n        <ThumbnailImage\n          src={imageError ? '/images/game-placeholder.jpg' : image}\n          alt={title}\n          className=\"w-full h-full object-cover transition-transform duration-300 group-hover:scale-110\"\n          onError={() => setImageError(true)}\n          sizes=\"(max-width: 640px) 50vw, (max-width: 1024px) 33vw, 25vw\"\n        />\n\n        {/* Overlay com gradiente */}\n        <div className=\"absolute inset-0 bg-gradient-to-t from-black/80 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\" />\n\n        {/* Badges */}\n        <div className=\"absolute top-2 left-2 flex flex-wrap gap-1\">\n          {isNew && (\n            <span className=\"bg-green-500 text-white text-xs px-2 py-1 rounded-full font-semibold\">\n              NOVO\n            </span>\n          )}\n          {isHot && (\n            <span className=\"bg-red-500 text-white text-xs px-2 py-1 rounded-full font-semibold\">\n              🔥 HOT\n            </span>\n          )}\n        </div>\n\n        {/* Botão de favorito */}\n        <button\n          onClick={handleFavorite}\n          className={`absolute top-2 right-2 p-2 rounded-full transition-all duration-200 ${\n            isFavorite\n              ? 'bg-red-500 text-white'\n              : 'bg-black/50 text-white hover:bg-red-500'\n          }`}\n          aria-label={isFavorite ? 'Remover dos favoritos' : 'Adicionar aos favoritos'}\n        >\n          <svg className=\"w-4 h-4\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path d=\"M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z\" />\n          </svg>\n        </button>\n\n        {/* Botão de play (aparece no hover) */}\n        <div className=\"absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300\">\n          <button\n            onClick={handlePlay}\n            className=\"bg-yellow-500 hover:bg-yellow-600 text-black p-4 rounded-full shadow-lg transform scale-90 group-hover:scale-100 transition-transform duration-200\"\n            aria-label={`Jogar ${title}`}\n          >\n            <svg className=\"w-6 h-6\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path d=\"M8 5v14l11-7z\" />\n            </svg>\n          </button>\n        </div>\n      </div>\n\n      {/* Informações do jogo */}\n      <div className=\"p-4\">\n        <div className=\"flex items-start justify-between mb-2\">\n          <h3 className=\"text-white font-semibold text-sm line-clamp-2 group-hover:text-yellow-500 transition-colors\">\n            {title}\n          </h3>\n          {rating > 0 && (\n            <div className=\"flex items-center ml-2 flex-shrink-0\">\n              <svg className=\"w-4 h-4 text-yellow-500\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path d=\"M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z\" />\n              </svg>\n              <span className=\"text-gray-300 text-xs ml-1\">{rating.toFixed(1)}</span>\n            </div>\n          )}\n        </div>\n\n        <div className=\"flex items-center justify-between text-xs text-gray-400\">\n          <span className=\"bg-gray-700 px-2 py-1 rounded\">{category}</span>\n          {provider && (\n            <span className=\"truncate ml-2\">{provider}</span>\n          )}\n        </div>\n\n        {description && (\n          <p className=\"text-gray-400 text-xs mt-2 line-clamp-2\">\n            {description}\n          </p>\n        )}\n\n        {/* Botões de ação (mobile) */}\n        <div className=\"flex gap-2 mt-3 md:hidden\">\n          <button\n            onClick={handlePlay}\n            className=\"flex-1 bg-yellow-500 hover:bg-yellow-600 text-black py-2 px-3 rounded-lg text-sm font-semibold transition-colors\"\n          >\n            Jogar\n          </button>\n          <button\n            onClick={handleFavorite}\n            className={`p-2 rounded-lg transition-colors ${\n              isFavorite\n                ? 'bg-red-500 text-white'\n                : 'bg-gray-700 text-gray-300 hover:bg-red-500 hover:text-white'\n            }`}\n          >\n            <svg className=\"w-4 h-4\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path d=\"M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z\" />\n            </svg>\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n\n  // Se há href, envolver em Link\n  if (href) {\n    return (\n      <Link href={href} className=\"block\">\n        <CardContent />\n      </Link>\n    );\n  }\n\n  return <CardContent />;\n}\n\n// Componente para grid de jogos responsivo\nexport function GameGrid({ \n  children, \n  className = '' \n}: { \n  children: React.ReactNode; \n  className?: string; \n}) {\n  return (\n    <div className={`grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 2xl:grid-cols-6 gap-4 ${className}`}>\n      {children}\n    </div>\n  );\n}\n\n// Componente para skeleton loading\nexport function GameCardSkeleton() {\n  return (\n    <div className=\"bg-gray-800 rounded-xl overflow-hidden animate-pulse\">\n      <div className=\"aspect-[4/3] bg-gray-700\" />\n      <div className=\"p-4\">\n        <div className=\"h-4 bg-gray-700 rounded mb-2\" />\n        <div className=\"h-3 bg-gray-700 rounded w-2/3\" />\n      </div>\n    </div>\n  );\n}\n\n// Hook para gerenciar favoritos\nexport function useFavorites() {\n  const [favorites, setFavorites] = useState<string[]>([]);\n\n  const toggleFavorite = (gameId: string) => {\n    setFavorites(prev => \n      prev.includes(gameId)\n        ? prev.filter(id => id !== gameId)\n        : [...prev, gameId]\n    );\n  };\n\n  const isFavorite = (gameId: string) => favorites.includes(gameId);\n\n  return { favorites, toggleFavorite, isFavorite };\n}\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAyBe,SAAS,SAAS,EAC/B,EAAE,EACF,KAAK,EACL,KAAK,EACL,QAAQ,EACR,SAAS,CAAC,EACV,QAAQ,KAAK,EACb,QAAQ,KAAK,EACb,QAAQ,EACR,WAAW,EACX,IAAI,EACJ,MAAM,EACN,UAAU,EACV,aAAa,KAAK,EAClB,iBAAiB,EACjB,kBAAkB,IAAI,EACR;;IACd,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,uCAAuC;IACvC,MAAM,uBAAuB;QAC3B,IAAI,mBAAmB,OAAO;QAE9B,8CAA8C;QAC9C,MAAM,cAAgF;YACpF,SAAS;YACT,QAAQ;YACR,SAAS;YACT,YAAY;YACZ,WAAW;YACX,YAAY;YACZ,SAAS;QACX;QAEA,OAAO,WAAW,CAAC,SAAS,IAAI;IAClC;IAEA,MAAM,aAAa,CAAC;QAClB,EAAE,cAAc;QAEhB,IAAI,iBAAiB;YACnB,kDAAkD;YAClD,MAAM,cAAc;YACpB,MAAM,YAAY,CAAA,GAAA,0HAAA,CAAA,sBAAmB,AAAD,EAAE;YAEtC,IAAI,WAAW;gBACb,CAAA,GAAA,0HAAA,CAAA,kBAAe,AAAD,EAAE;oBACd,MAAM;oBACN,aAAa,UAAU,EAAE;oBACzB,UAAU;wBACR,SAAS;wBACT,YAAY;wBACZ,eAAe;wBACf,UAAU;oBACZ;gBACF;gBAEA,MAAM,eAAe,CAAA,GAAA,0HAAA,CAAA,uBAAoB,AAAD,EAAE,UAAU,EAAE,EAAE;oBACtD,MAAM;oBACN,UAAU;oBACV,QAAQ;gBACV;gBAEA,OAAO,IAAI,CAAC,cAAc,UAAU;gBACpC;YACF;QACF;QAEA;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,EAAE,cAAc;QAChB,EAAE,eAAe;QACjB;IACF;IAEA,MAAM,cAAc,kBAClB,6LAAC;YACC,WAAW,CAAC,6HAA6H,EACvI,YAAY,2BAA2B,IACvC;YACF,cAAc,IAAM,aAAa;YACjC,cAAc,IAAM,aAAa;;8BAGjC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,uIAAA,CAAA,iBAAc;4BACb,KAAK,aAAa,iCAAiC;4BACnD,KAAK;4BACL,WAAU;4BACV,SAAS,IAAM,cAAc;4BAC7B,OAAM;;;;;;sCAIR,6LAAC;4BAAI,WAAU;;;;;;sCAGf,6LAAC;4BAAI,WAAU;;gCACZ,uBACC,6LAAC;oCAAK,WAAU;8CAAuE;;;;;;gCAIxF,uBACC,6LAAC;oCAAK,WAAU;8CAAqE;;;;;;;;;;;;sCAOzF,6LAAC;4BACC,SAAS;4BACT,WAAW,CAAC,oEAAoE,EAC9E,aACI,0BACA,2CACJ;4BACF,cAAY,aAAa,0BAA0B;sCAEnD,cAAA,6LAAC;gCAAI,WAAU;gCAAU,MAAK;gCAAe,SAAQ;0CACnD,cAAA,6LAAC;oCAAK,GAAE;;;;;;;;;;;;;;;;sCAKZ,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,SAAS;gCACT,WAAU;gCACV,cAAY,CAAC,MAAM,EAAE,OAAO;0CAE5B,cAAA,6LAAC;oCAAI,WAAU;oCAAU,MAAK;oCAAe,SAAQ;8CACnD,cAAA,6LAAC;wCAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAOhB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CACX;;;;;;gCAEF,SAAS,mBACR,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;4CAA0B,MAAK;4CAAe,SAAQ;sDACnE,cAAA,6LAAC;gDAAK,GAAE;;;;;;;;;;;sDAEV,6LAAC;4CAAK,WAAU;sDAA8B,OAAO,OAAO,CAAC;;;;;;;;;;;;;;;;;;sCAKnE,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAK,WAAU;8CAAiC;;;;;;gCAChD,0BACC,6LAAC;oCAAK,WAAU;8CAAiB;;;;;;;;;;;;wBAIpC,6BACC,6LAAC;4BAAE,WAAU;sCACV;;;;;;sCAKL,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS;oCACT,WAAU;8CACX;;;;;;8CAGD,6LAAC;oCACC,SAAS;oCACT,WAAW,CAAC,iCAAiC,EAC3C,aACI,0BACA,+DACJ;8CAEF,cAAA,6LAAC;wCAAI,WAAU;wCAAU,MAAK;wCAAe,SAAQ;kDACnD,cAAA,6LAAC;4CAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAQpB,+BAA+B;IAC/B,IAAI,MAAM;QACR,qBACE,6LAAC,+JAAA,CAAA,UAAI;YAAC,MAAM;YAAM,WAAU;sBAC1B,cAAA,6LAAC;;;;;;;;;;IAGP;IAEA,qBAAO,6LAAC;;;;;AACV;GA9MwB;KAAA;AAiNjB,SAAS,SAAS,EACvB,QAAQ,EACR,YAAY,EAAE,EAIf;IACC,qBACE,6LAAC;QAAI,WAAW,CAAC,oFAAoF,EAAE,WAAW;kBAC/G;;;;;;AAGP;MAZgB;AAeT,SAAS;IACd,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;;;;;0BACf,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;;;;;;;;;;;;;AAIvB;MAVgB;AAaT,SAAS;;IACd,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAEvD,MAAM,iBAAiB,CAAC;QACtB,aAAa,CAAA,OACX,KAAK,QAAQ,CAAC,UACV,KAAK,MAAM,CAAC,CAAA,KAAM,OAAO,UACzB;mBAAI;gBAAM;aAAO;IAEzB;IAEA,MAAM,aAAa,CAAC,SAAmB,UAAU,QAAQ,CAAC;IAE1D,OAAO;QAAE;QAAW;QAAgB;IAAW;AACjD;IAdgB", "debugId": null}}, {"offset": {"line": 1259, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/%E6%96%87%E6%A1%A3/win222/win222-brasil/src/components/AffiliateButton.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { generateAffiliateUrl, trackAffiliateClick, AFFILIATE_LINKS } from '@/lib/affiliate';\n\ninterface AffiliateButtonProps {\n  affiliateId: string;\n  variant?: 'primary' | 'secondary' | 'outline' | 'gradient';\n  size?: 'sm' | 'md' | 'lg' | 'xl';\n  children: React.ReactNode;\n  className?: string;\n  additionalParams?: Record<string, string>;\n  context?: string;\n  fullWidth?: boolean;\n  icon?: React.ReactNode;\n  loading?: boolean;\n  disabled?: boolean;\n}\n\nconst AffiliateButton: React.FC<AffiliateButtonProps> = ({\n  affiliateId,\n  variant = 'primary',\n  size = 'md',\n  children,\n  className = '',\n  additionalParams,\n  context,\n  fullWidth = false,\n  icon,\n  loading = false,\n  disabled = false\n}) => {\n  const [isClicked, setIsClicked] = useState(false);\n  \n  const affiliate = AFFILIATE_LINKS[affiliateId];\n  \n  if (!affiliate || !affiliate.isActive) {\n    return null;\n  }\n\n  const handleClick = () => {\n    if (disabled || loading) return;\n    \n    setIsClicked(true);\n    trackAffiliateClick(affiliateId, context);\n    \n    // Reset clicked state after animation\n    setTimeout(() => setIsClicked(false), 200);\n    \n    // Open affiliate link\n    const url = generateAffiliateUrl(affiliateId, additionalParams);\n    window.open(url, '_blank', 'noopener,noreferrer');\n  };\n\n  // Base styles\n  const baseStyles = `\n    inline-flex items-center justify-center font-semibold transition-all duration-300 \n    transform hover:scale-105 active:scale-95 focus:outline-none focus:ring-4 \n    focus:ring-yellow-500/50 disabled:opacity-50 disabled:cursor-not-allowed\n    disabled:transform-none relative overflow-hidden\n  `;\n\n  // Size styles\n  const sizeStyles = {\n    sm: 'px-3 py-2 text-sm gap-2',\n    md: 'px-6 py-3 text-base gap-2',\n    lg: 'px-8 py-4 text-lg gap-3',\n    xl: 'px-10 py-5 text-xl gap-3'\n  };\n\n  // Variant styles\n  const variantStyles = {\n    primary: `\n      bg-yellow-500 hover:bg-yellow-600 text-black shadow-lg hover:shadow-xl\n      border-2 border-yellow-500 hover:border-yellow-600\n    `,\n    secondary: `\n      bg-gray-700 hover:bg-gray-600 text-white shadow-lg hover:shadow-xl\n      border-2 border-gray-700 hover:border-gray-600\n    `,\n    outline: `\n      bg-transparent hover:bg-yellow-500 text-yellow-500 hover:text-black\n      border-2 border-yellow-500 shadow-lg hover:shadow-xl\n    `,\n    gradient: `\n      bg-gradient-to-r from-yellow-500 to-orange-500 hover:from-yellow-600 \n      hover:to-orange-600 text-black shadow-lg hover:shadow-xl border-2 \n      border-transparent\n    `\n  };\n\n  const buttonClasses = `\n    ${baseStyles}\n    ${sizeStyles[size]}\n    ${variantStyles[variant]}\n    ${fullWidth ? 'w-full' : ''}\n    ${isClicked ? 'animate-pulse' : ''}\n    ${className}\n  `;\n\n  return (\n    <button\n      type=\"button\"\n      className={buttonClasses}\n      onClick={handleClick}\n      disabled={disabled || loading}\n      aria-label={`Acessar ${affiliate.name}`}\n    >\n      {/* Loading spinner */}\n      {loading && (\n        <div className=\"absolute inset-0 flex items-center justify-center bg-inherit\">\n          <div className=\"w-5 h-5 border-2 border-current border-t-transparent rounded-full animate-spin\" />\n        </div>\n      )}\n      \n      {/* Button content */}\n      <div className={`flex items-center gap-2 ${loading ? 'opacity-0' : 'opacity-100'}`}>\n        {icon && <span className=\"flex-shrink-0\">{icon}</span>}\n        <span>{children}</span>\n        \n        {/* External link icon */}\n        <svg \n          className=\"w-4 h-4 flex-shrink-0\" \n          fill=\"none\" \n          stroke=\"currentColor\" \n          viewBox=\"0 0 24 24\"\n        >\n          <path \n            strokeLinecap=\"round\" \n            strokeLinejoin=\"round\" \n            strokeWidth={2} \n            d=\"M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14\" \n          />\n        </svg>\n      </div>\n\n      {/* Ripple effect */}\n      {isClicked && (\n        <div className=\"absolute inset-0 bg-white/20 rounded-lg animate-ping\" />\n      )}\n    </button>\n  );\n};\n\nexport default AffiliateButton;\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAmBA,MAAM,kBAAkD,CAAC,EACvD,WAAW,EACX,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,QAAQ,EACR,YAAY,EAAE,EACd,gBAAgB,EAChB,OAAO,EACP,YAAY,KAAK,EACjB,IAAI,EACJ,UAAU,KAAK,EACf,WAAW,KAAK,EACjB;;IACC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,YAAY,0HAAA,CAAA,kBAAe,CAAC,YAAY;IAE9C,IAAI,CAAC,aAAa,CAAC,UAAU,QAAQ,EAAE;QACrC,OAAO;IACT;IAEA,MAAM,cAAc;QAClB,IAAI,YAAY,SAAS;QAEzB,aAAa;QACb,CAAA,GAAA,0HAAA,CAAA,sBAAmB,AAAD,EAAE,aAAa;QAEjC,sCAAsC;QACtC,WAAW,IAAM,aAAa,QAAQ;QAEtC,sBAAsB;QACtB,MAAM,MAAM,CAAA,GAAA,0HAAA,CAAA,uBAAoB,AAAD,EAAE,aAAa;QAC9C,OAAO,IAAI,CAAC,KAAK,UAAU;IAC7B;IAEA,cAAc;IACd,MAAM,aAAa,CAAC;;;;;EAKpB,CAAC;IAED,cAAc;IACd,MAAM,aAAa;QACjB,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,iBAAiB;IACjB,MAAM,gBAAgB;QACpB,SAAS,CAAC;;;IAGV,CAAC;QACD,WAAW,CAAC;;;IAGZ,CAAC;QACD,SAAS,CAAC;;;IAGV,CAAC;QACD,UAAU,CAAC;;;;IAIX,CAAC;IACH;IAEA,MAAM,gBAAgB,CAAC;IACrB,EAAE,WAAW;IACb,EAAE,UAAU,CAAC,KAAK,CAAC;IACnB,EAAE,aAAa,CAAC,QAAQ,CAAC;IACzB,EAAE,YAAY,WAAW,GAAG;IAC5B,EAAE,YAAY,kBAAkB,GAAG;IACnC,EAAE,UAAU;EACd,CAAC;IAED,qBACE,6LAAC;QACC,MAAK;QACL,WAAW;QACX,SAAS;QACT,UAAU,YAAY;QACtB,cAAY,CAAC,QAAQ,EAAE,UAAU,IAAI,EAAE;;YAGtC,yBACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;;;;;;;;;;0BAKnB,6LAAC;gBAAI,WAAW,CAAC,wBAAwB,EAAE,UAAU,cAAc,eAAe;;oBAC/E,sBAAQ,6LAAC;wBAAK,WAAU;kCAAiB;;;;;;kCAC1C,6LAAC;kCAAM;;;;;;kCAGP,6LAAC;wBACC,WAAU;wBACV,MAAK;wBACL,QAAO;wBACP,SAAQ;kCAER,cAAA,6LAAC;4BACC,eAAc;4BACd,gBAAe;4BACf,aAAa;4BACb,GAAE;;;;;;;;;;;;;;;;;YAMP,2BACC,6LAAC;gBAAI,WAAU;;;;;;;;;;;;AAIvB;GA3HM;KAAA;uCA6HS", "debugId": null}}, {"offset": {"line": 1422, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/%E6%96%87%E6%A1%A3/win222/win222-brasil/src/components/ConversionBanner.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport AffiliateButton from './AffiliateButton';\nimport { trackConversion } from '@/lib/affiliate';\n\ninterface ConversionBannerProps {\n  type?: 'welcome' | 'bonus' | 'urgency' | 'social-proof';\n  position?: 'top' | 'bottom' | 'floating';\n  dismissible?: boolean;\n  autoShow?: boolean;\n  delay?: number;\n}\n\nconst ConversionBanner: React.FC<ConversionBannerProps> = ({\n  type = 'welcome',\n  position = 'top',\n  dismissible = true,\n  autoShow = true,\n  delay = 3000\n}) => {\n  const [isVisible, setIsVisible] = useState(false);\n  const [isDismissed, setIsDismissed] = useState(false);\n\n  useEffect(() => {\n    // Check if banner was previously dismissed\n    const dismissed = localStorage.getItem(`banner-dismissed-${type}`);\n    if (dismissed) {\n      setIsDismissed(true);\n      return;\n    }\n\n    if (autoShow) {\n      const timer = setTimeout(() => {\n        setIsVisible(true);\n        trackConversion({\n          type: 'view',\n          affiliateId: 'main_casino',\n          metadata: { banner_type: type, position }\n        });\n      }, delay);\n\n      return () => clearTimeout(timer);\n    }\n  }, [autoShow, delay, type, position]);\n\n  const handleDismiss = () => {\n    setIsVisible(false);\n    setIsDismissed(true);\n    localStorage.setItem(`banner-dismissed-${type}`, 'true');\n  };\n\n  if (isDismissed || !isVisible) {\n    return null;\n  }\n\n  const bannerContent = {\n    welcome: {\n      title: '🎉 Bem-vindo ao Win222 Brasil!',\n      subtitle: 'Ganhe até R$ 5.000 + 200 rodadas grátis no seu primeiro depósito',\n      cta: 'Cadastrar Agora',\n      affiliateId: 'welcome_bonus',\n      bgClass: 'bg-gradient-to-r from-yellow-500 to-orange-500',\n      textClass: 'text-black'\n    },\n    bonus: {\n      title: '💰 Oferta Especial Limitada!',\n      subtitle: 'Bônus de 100% até R$ 1.000 + giros grátis válido por 24h',\n      cta: 'Resgatar Bônus',\n      affiliateId: 'welcome_bonus',\n      bgClass: 'bg-gradient-to-r from-purple-600 to-pink-600',\n      textClass: 'text-white'\n    },\n    urgency: {\n      title: '⏰ Últimas Horas!',\n      subtitle: 'Promoção especial termina em breve. Não perca esta oportunidade!',\n      cta: 'Aproveitar Agora',\n      affiliateId: 'main_casino',\n      bgClass: 'bg-gradient-to-r from-red-600 to-red-700',\n      textClass: 'text-white'\n    },\n    'social-proof': {\n      title: '🏆 +50.000 jogadores confiam em nós',\n      subtitle: 'Junte-se aos vencedores e comece a ganhar hoje mesmo',\n      cta: 'Começar a Jogar',\n      affiliateId: 'main_casino',\n      bgClass: 'bg-gradient-to-r from-green-600 to-blue-600',\n      textClass: 'text-white'\n    }\n  };\n\n  const content = bannerContent[type];\n\n  const positionClasses = {\n    top: 'top-0 left-0 right-0',\n    bottom: 'bottom-0 left-0 right-0',\n    floating: 'bottom-4 left-4 right-4 rounded-lg shadow-2xl'\n  };\n\n  return (\n    <div\n      className={`\n        fixed z-50 ${positionClasses[position]} ${content.bgClass} ${content.textClass}\n        transform transition-all duration-500 ease-out\n        ${isVisible ? 'translate-y-0 opacity-100' : \n          position === 'top' ? '-translate-y-full opacity-0' : \n          position === 'bottom' ? 'translate-y-full opacity-0' : \n          'translate-y-full opacity-0'}\n      `}\n    >\n      <div className=\"container mx-auto px-4 py-3\">\n        <div className=\"flex items-center justify-between gap-4\">\n          {/* Content */}\n          <div className=\"flex-1 min-w-0\">\n            <div className=\"flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-4\">\n              <div className=\"flex-1 min-w-0\">\n                <h3 className=\"font-bold text-sm sm:text-base truncate\">\n                  {content.title}\n                </h3>\n                <p className=\"text-xs sm:text-sm opacity-90 line-clamp-2\">\n                  {content.subtitle}\n                </p>\n              </div>\n              \n              {/* CTA Button */}\n              <div className=\"flex-shrink-0\">\n                <AffiliateButton\n                  affiliateId={content.affiliateId}\n                  variant={content.textClass === 'text-black' ? 'secondary' : 'primary'}\n                  size=\"sm\"\n                  context={`banner-${type}`}\n                  additionalParams={{ banner_position: position }}\n                >\n                  {content.cta}\n                </AffiliateButton>\n              </div>\n            </div>\n          </div>\n\n          {/* Dismiss Button */}\n          {dismissible && (\n            <button\n              onClick={handleDismiss}\n              className={`\n                flex-shrink-0 p-1 rounded-full transition-colors duration-200\n                ${content.textClass === 'text-black' \n                  ? 'hover:bg-black/10 text-black/70 hover:text-black' \n                  : 'hover:bg-white/10 text-white/70 hover:text-white'}\n              `}\n              aria-label=\"Fechar banner\"\n            >\n              <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n              </svg>\n            </button>\n          )}\n        </div>\n      </div>\n\n      {/* Progress bar for urgency banners */}\n      {type === 'urgency' && (\n        <div className=\"absolute bottom-0 left-0 right-0 h-1 bg-white/20\">\n          <div \n            className=\"h-full bg-white/50 transition-all duration-1000 ease-linear\"\n            style={{ \n              width: '100%',\n              animation: 'countdown 24h linear forwards'\n            }}\n          />\n        </div>\n      )}\n\n      <style jsx>{`\n        @keyframes countdown {\n          from { width: 100%; }\n          to { width: 0%; }\n        }\n      `}</style>\n    </div>\n  );\n};\n\nexport default ConversionBanner;\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;;;AAJA;;;;;AAcA,MAAM,mBAAoD,CAAC,EACzD,OAAO,SAAS,EAChB,WAAW,KAAK,EAChB,cAAc,IAAI,EAClB,WAAW,IAAI,EACf,QAAQ,IAAI,EACb;;IACC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,2CAA2C;YAC3C,MAAM,YAAY,aAAa,OAAO,CAAC,CAAC,iBAAiB,EAAE,MAAM;YACjE,IAAI,WAAW;gBACb,eAAe;gBACf;YACF;YAEA,IAAI,UAAU;gBACZ,MAAM,QAAQ;wDAAW;wBACvB,aAAa;wBACb,CAAA,GAAA,0HAAA,CAAA,kBAAe,AAAD,EAAE;4BACd,MAAM;4BACN,aAAa;4BACb,UAAU;gCAAE,aAAa;gCAAM;4BAAS;wBAC1C;oBACF;uDAAG;gBAEH;kDAAO,IAAM,aAAa;;YAC5B;QACF;qCAAG;QAAC;QAAU;QAAO;QAAM;KAAS;IAEpC,MAAM,gBAAgB;QACpB,aAAa;QACb,eAAe;QACf,aAAa,OAAO,CAAC,CAAC,iBAAiB,EAAE,MAAM,EAAE;IACnD;IAEA,IAAI,eAAe,CAAC,WAAW;QAC7B,OAAO;IACT;IAEA,MAAM,gBAAgB;QACpB,SAAS;YACP,OAAO;YACP,UAAU;YACV,KAAK;YACL,aAAa;YACb,SAAS;YACT,WAAW;QACb;QACA,OAAO;YACL,OAAO;YACP,UAAU;YACV,KAAK;YACL,aAAa;YACb,SAAS;YACT,WAAW;QACb;QACA,SAAS;YACP,OAAO;YACP,UAAU;YACV,KAAK;YACL,aAAa;YACb,SAAS;YACT,WAAW;QACb;QACA,gBAAgB;YACd,OAAO;YACP,UAAU;YACV,KAAK;YACL,aAAa;YACb,SAAS;YACT,WAAW;QACb;IACF;IAEA,MAAM,UAAU,aAAa,CAAC,KAAK;IAEnC,MAAM,kBAAkB;QACtB,KAAK;QACL,QAAQ;QACR,UAAU;IACZ;IAEA,qBACE,6LAAC;kDACY,CAAC;mBACC,EAAE,eAAe,CAAC,SAAS,CAAC,CAAC,EAAE,QAAQ,OAAO,CAAC,CAAC,EAAE,QAAQ,SAAS,CAAC;;QAE/E,EAAE,YAAY,8BACZ,aAAa,QAAQ,gCACrB,aAAa,WAAW,+BACxB,6BAA6B;MACjC,CAAC;;0BAED,6LAAC;0DAAc;0BACb,cAAA,6LAAC;8DAAc;;sCAEb,6LAAC;sEAAc;sCACb,cAAA,6LAAC;0EAAc;;kDACb,6LAAC;kFAAc;;0DACb,6LAAC;0FAAa;0DACX,QAAQ,KAAK;;;;;;0DAEhB,6LAAC;0FAAY;0DACV,QAAQ,QAAQ;;;;;;;;;;;;kDAKrB,6LAAC;kFAAc;kDACb,cAAA,6LAAC,wIAAA,CAAA,UAAe;4CACd,aAAa,QAAQ,WAAW;4CAChC,SAAS,QAAQ,SAAS,KAAK,eAAe,cAAc;4CAC5D,MAAK;4CACL,SAAS,CAAC,OAAO,EAAE,MAAM;4CACzB,kBAAkB;gDAAE,iBAAiB;4CAAS;sDAE7C,QAAQ,GAAG;;;;;;;;;;;;;;;;;;;;;;wBAOnB,6BACC,6LAAC;4BACC,SAAS;4BAOT,cAAW;sEANA,CAAC;;gBAEV,EAAE,QAAQ,SAAS,KAAK,eACpB,qDACA,mDAAmD;cACzD,CAAC;sCAGD,cAAA,6LAAC;gCAAwB,MAAK;gCAAO,QAAO;gCAAe,SAAQ;0EAApD;0CACb,cAAA,6LAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAQ9E,SAAS,2BACR,6LAAC;0DAAc;0BACb,cAAA,6LAAC;oBAEC,OAAO;wBACL,OAAO;wBACP,WAAW;oBACb;8DAJU;;;;;;;;;;;;;;;;;;;;;AAiBtB;GAtKM;KAAA;uCAwKS", "debugId": null}}, {"offset": {"line": 1683, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/%E6%96%87%E6%A1%A3/win222/win222-brasil/src/components/HomePage.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport MobileNavigation from './MobileNavigation';\nimport { HeroImage, ThumbnailImage } from './OptimizedImage';\nimport GameCard, { GameGrid, useFavorites } from './GameCard';\nimport AffiliateButton from './AffiliateButton';\nimport ConversionBanner from './ConversionBanner';\n\n// Dados de exemplo para jogos\nconst featuredGames = [\n  {\n    id: '1',\n    title: 'Fortune Tiger',\n    image: '/images/games/fortune-tiger.jpg',\n    category: 'Slots',\n    rating: 4.8,\n    isHot: true,\n    provider: 'PG Soft',\n    description: 'Jogo de slots com tema oriental e grandes prêmios',\n    affiliateCategory: 'slots' as const,\n  },\n  {\n    id: '2',\n    title: 'Aviator',\n    image: '/images/games/aviator.jpg',\n    category: 'Crash',\n    rating: 4.9,\n    isNew: true,\n    provider: 'Spribe',\n    description: 'Jogo de multiplicador emocionante',\n    affiliateCategory: 'casino' as const,\n  },\n  {\n    id: '3',\n    title: 'Blackjack Premium',\n    image: '/images/games/blackjack.jpg',\n    category: 'Mesa',\n    rating: 4.7,\n    provider: 'Evolution',\n    description: 'Blackjack clássico com dealers ao vivo',\n    affiliateCategory: 'live' as const,\n  },\n  {\n    id: '4',\n    title: 'Roleta Brasileira',\n    image: '/images/games/roleta.jpg',\n    category: 'Mesa',\n    rating: 4.6,\n    provider: 'Pragmatic',\n    description: 'Roleta com regras brasileiras',\n    affiliateCategory: 'casino' as const,\n  },\n  {\n    id: '5',\n    title: 'Sweet Bonanza',\n    image: '/images/games/sweet-bonanza.jpg',\n    category: 'Slots',\n    rating: 4.8,\n    isHot: true,\n    provider: 'Pragmatic',\n    description: 'Slot doce com multiplicadores',\n    affiliateCategory: 'slots' as const,\n  },\n  {\n    id: '6',\n    title: 'Mines',\n    image: '/images/games/mines.jpg',\n    category: 'Original',\n    rating: 4.5,\n    isNew: true,\n    provider: 'Win222',\n    description: 'Jogo original de estratégia e sorte',\n    affiliateCategory: 'casino' as const,\n  },\n];\n\nconst categories = [\n  { name: 'Slots', icon: '🎰', count: 500 },\n  { name: 'Mesa', icon: '🃏', count: 50 },\n  { name: 'Crash', icon: '🚀', count: 25 },\n  { name: 'Original', icon: '⭐', count: 30 },\n  { name: 'Ao Vivo', icon: '📹', count: 40 },\n  { name: 'Esportes', icon: '⚽', count: 100 },\n];\n\nexport default function HomePage() {\n  const [isLoaded, setIsLoaded] = useState(false);\n  const { favorites, toggleFavorite, isFavorite } = useFavorites();\n\n  useEffect(() => {\n    setIsLoaded(true);\n  }, []);\n\n  const handlePlayGame = (gameId: string) => {\n    console.log(`Playing game: ${gameId}`);\n    // Aqui você implementaria a lógica para abrir o jogo\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-900\">\n      <MobileNavigation />\n\n      {/* Conversion Banners */}\n      <ConversionBanner\n        type=\"welcome\"\n        position=\"top\"\n        dismissible={true}\n        autoShow={true}\n        delay={5000}\n      />\n\n      {/* Hero Section */}\n      <section className=\"relative overflow-hidden\">\n        <div className=\"win222-gradient min-h-[60vh] flex items-center\">\n          <div className=\"container mx-auto px-4 py-12\">\n            <div className=\"grid lg:grid-cols-2 gap-8 items-center\">\n              <div className={`text-center lg:text-left ${isLoaded ? 'animate-fade-in' : 'opacity-0'}`}>\n                <h1 className=\"text-4xl md:text-6xl font-bold text-white mb-6\">\n                  <span className=\"win222-gold\">Win222</span>\n                  <br />\n                  Brasil\n                </h1>\n                <p className=\"text-xl text-gray-300 mb-8 max-w-lg\">\n                  A plataforma líder em jogos online no Brasil com mais de 1000 jogos seguros, \n                  bônus exclusivos e suporte 24/7.\n                </p>\n                <div className=\"flex flex-col sm:flex-row gap-4 justify-center lg:justify-start\">\n                  <AffiliateButton\n                    affiliateId=\"welcome_bonus\"\n                    variant=\"primary\"\n                    size=\"lg\"\n                    context=\"hero-cta\"\n                    additionalParams={{ source: 'homepage_hero' }}\n                  >\n                    Cadastrar Grátis\n                  </AffiliateButton>\n                  <AffiliateButton\n                    affiliateId=\"main_casino\"\n                    variant=\"outline\"\n                    size=\"lg\"\n                    context=\"hero-games\"\n                    additionalParams={{ source: 'homepage_hero' }}\n                  >\n                    Ver Jogos\n                  </AffiliateButton>\n                </div>\n              </div>\n              <div className={`hidden lg:block ${isLoaded ? 'animate-slide-up' : 'opacity-0'}`}>\n                <HeroImage\n                  src=\"/images/hero-games.jpg\"\n                  alt=\"Jogos Win222 Brasil\"\n                  width={600}\n                  height={400}\n                  className=\"rounded-2xl shadow-2xl\"\n                />\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Floating stats */}\n        <div className=\"absolute bottom-0 left-0 right-0 transform translate-y-1/2\">\n          <div className=\"container mx-auto px-4\">\n            <div className=\"bg-gray-800 rounded-2xl shadow-2xl p-6\">\n              <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4 text-center\">\n                <div>\n                  <div className=\"text-2xl font-bold text-yellow-500\">1000+</div>\n                  <div className=\"text-gray-400 text-sm\">Jogos</div>\n                </div>\n                <div>\n                  <div className=\"text-2xl font-bold text-yellow-500\">50K+</div>\n                  <div className=\"text-gray-400 text-sm\">Jogadores</div>\n                </div>\n                <div>\n                  <div className=\"text-2xl font-bold text-yellow-500\">24/7</div>\n                  <div className=\"text-gray-400 text-sm\">Suporte</div>\n                </div>\n                <div>\n                  <div className=\"text-2xl font-bold text-yellow-500\">R$ 1M+</div>\n                  <div className=\"text-gray-400 text-sm\">Prêmios</div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Categories Section */}\n      <section className=\"py-20 mt-16\">\n        <div className=\"container mx-auto px-4\">\n          <h2 className=\"text-3xl font-bold text-white text-center mb-12\">\n            Categorias de Jogos\n          </h2>\n          <div className=\"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4\">\n            {categories.map((category, index) => (\n              <div\n                key={category.name}\n                className={`bg-gray-800 hover:bg-gray-700 p-6 rounded-xl text-center transition-all duration-300 hover-optimized cursor-pointer ${\n                  isLoaded ? 'animate-fade-in' : 'opacity-0'\n                }`}\n                style={{ animationDelay: `${index * 100}ms` }}\n              >\n                <div className=\"text-3xl mb-2\">{category.icon}</div>\n                <h3 className=\"text-white font-semibold mb-1\">{category.name}</h3>\n                <p className=\"text-gray-400 text-sm\">{category.count} jogos</p>\n              </div>\n            ))}\n          </div>\n        </div>\n      </section>\n\n      {/* Featured Games Section */}\n      <section className=\"py-20 bg-gray-800/50\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"flex items-center justify-between mb-12\">\n            <h2 className=\"text-3xl font-bold text-white\">\n              Jogos em Destaque\n            </h2>\n            <button className=\"text-yellow-500 hover:text-yellow-400 font-semibold\">\n              Ver Todos →\n            </button>\n          </div>\n          \n          <GameGrid>\n            {featuredGames.map((game, index) => (\n              <div\n                key={game.id}\n                className={`${isLoaded ? 'animate-fade-in' : 'opacity-0'}`}\n                style={{ animationDelay: `${index * 150}ms` }}\n              >\n                <GameCard\n                  {...game}\n                  onPlay={() => handlePlayGame(game.id)}\n                  onFavorite={() => toggleFavorite(game.id)}\n                  isFavorite={isFavorite(game.id)}\n                />\n              </div>\n            ))}\n          </GameGrid>\n        </div>\n      </section>\n\n      {/* Promotions Section */}\n      <section className=\"py-20\">\n        <div className=\"container mx-auto px-4\">\n          <h2 className=\"text-3xl font-bold text-white text-center mb-12\">\n            Promoções Exclusivas\n          </h2>\n          <div className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-8\">\n            <div className=\"bg-gradient-to-r from-yellow-500 to-orange-500 p-8 rounded-2xl text-black\">\n              <h3 className=\"text-2xl font-bold mb-4\">Bônus de Boas-vindas</h3>\n              <p className=\"text-lg mb-4\">100% até R$ 1.000 + 100 giros grátis</p>\n              <AffiliateButton\n                affiliateId=\"welcome_bonus\"\n                variant=\"secondary\"\n                size=\"md\"\n                context=\"promo-welcome\"\n                additionalParams={{ promo: 'welcome_bonus' }}\n                className=\"bg-black text-white hover:bg-gray-800\"\n              >\n                Resgatar\n              </AffiliateButton>\n            </div>\n            <div className=\"bg-gradient-to-r from-purple-500 to-pink-500 p-8 rounded-2xl text-white\">\n              <h3 className=\"text-2xl font-bold mb-4\">Cashback Semanal</h3>\n              <p className=\"text-lg mb-4\">Até 15% de volta toda semana</p>\n              <AffiliateButton\n                affiliateId=\"main_casino\"\n                variant=\"secondary\"\n                size=\"md\"\n                context=\"promo-cashback\"\n                additionalParams={{ promo: 'cashback' }}\n                className=\"bg-white text-purple-500 hover:bg-gray-100\"\n              >\n                Participar\n              </AffiliateButton>\n            </div>\n            <div className=\"bg-gradient-to-r from-green-500 to-blue-500 p-8 rounded-2xl text-white md:col-span-2 lg:col-span-1\">\n              <h3 className=\"text-2xl font-bold mb-4\">Torneio Mensal</h3>\n              <p className=\"text-lg mb-4\">R$ 50.000 em prêmios</p>\n              <AffiliateButton\n                affiliateId=\"main_casino\"\n                variant=\"secondary\"\n                size=\"md\"\n                context=\"promo-tournament\"\n                additionalParams={{ promo: 'tournament' }}\n                className=\"bg-white text-green-500 hover:bg-gray-100\"\n              >\n                Inscrever-se\n              </AffiliateButton>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Footer */}\n      <footer className=\"bg-gray-900 border-t border-gray-700 py-12\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"grid md:grid-cols-4 gap-8\">\n            <div>\n              <h3 className=\"text-white font-bold text-lg mb-4\">Win222 Brasil</h3>\n              <p className=\"text-gray-400 text-sm\">\n                A plataforma de jogos online mais confiável do Brasil.\n              </p>\n            </div>\n            <div>\n              <h4 className=\"text-white font-semibold mb-4\">Jogos</h4>\n              <ul className=\"space-y-2 text-gray-400 text-sm\">\n                <li><a href=\"/slots\" className=\"hover:text-white\">Slots</a></li>\n                <li><a href=\"/mesa\" className=\"hover:text-white\">Jogos de Mesa</a></li>\n                <li><a href=\"/ao-vivo\" className=\"hover:text-white\">Cassino Ao Vivo</a></li>\n                <li><a href=\"/esportes\" className=\"hover:text-white\">Apostas Esportivas</a></li>\n              </ul>\n            </div>\n            <div>\n              <h4 className=\"text-white font-semibold mb-4\">Suporte</h4>\n              <ul className=\"space-y-2 text-gray-400 text-sm\">\n                <li><a href=\"/ajuda\" className=\"hover:text-white\">Central de Ajuda</a></li>\n                <li><a href=\"/contato\" className=\"hover:text-white\">Contato</a></li>\n                <li><a href=\"/chat\" className=\"hover:text-white\">Chat 24/7</a></li>\n              </ul>\n            </div>\n            <div>\n              <h4 className=\"text-white font-semibold mb-4\">Legal</h4>\n              <ul className=\"space-y-2 text-gray-400 text-sm\">\n                <li><a href=\"/termos\" className=\"hover:text-white\">Termos de Uso</a></li>\n                <li><a href=\"/privacidade\" className=\"hover:text-white\">Privacidade</a></li>\n                <li><a href=\"/responsavel\" className=\"hover:text-white\">Jogo Responsável</a></li>\n              </ul>\n            </div>\n          </div>\n          <div className=\"border-t border-gray-700 mt-8 pt-8 text-center text-gray-400 text-sm\">\n            <p>&copy; 2024 Win222 Brasil. Todos os direitos reservados.</p>\n          </div>\n        </div>\n      </footer>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;;;AAPA;;;;;;;AASA,8BAA8B;AAC9B,MAAM,gBAAgB;IACpB;QACE,IAAI;QACJ,OAAO;QACP,OAAO;QACP,UAAU;QACV,QAAQ;QACR,OAAO;QACP,UAAU;QACV,aAAa;QACb,mBAAmB;IACrB;IACA;QACE,IAAI;QACJ,OAAO;QACP,OAAO;QACP,UAAU;QACV,QAAQ;QACR,OAAO;QACP,UAAU;QACV,aAAa;QACb,mBAAmB;IACrB;IACA;QACE,IAAI;QACJ,OAAO;QACP,OAAO;QACP,UAAU;QACV,QAAQ;QACR,UAAU;QACV,aAAa;QACb,mBAAmB;IACrB;IACA;QACE,IAAI;QACJ,OAAO;QACP,OAAO;QACP,UAAU;QACV,QAAQ;QACR,UAAU;QACV,aAAa;QACb,mBAAmB;IACrB;IACA;QACE,IAAI;QACJ,OAAO;QACP,OAAO;QACP,UAAU;QACV,QAAQ;QACR,OAAO;QACP,UAAU;QACV,aAAa;QACb,mBAAmB;IACrB;IACA;QACE,IAAI;QACJ,OAAO;QACP,OAAO;QACP,UAAU;QACV,QAAQ;QACR,OAAO;QACP,UAAU;QACV,aAAa;QACb,mBAAmB;IACrB;CACD;AAED,MAAM,aAAa;IACjB;QAAE,MAAM;QAAS,MAAM;QAAM,OAAO;IAAI;IACxC;QAAE,MAAM;QAAQ,MAAM;QAAM,OAAO;IAAG;IACtC;QAAE,MAAM;QAAS,MAAM;QAAM,OAAO;IAAG;IACvC;QAAE,MAAM;QAAY,MAAM;QAAK,OAAO;IAAG;IACzC;QAAE,MAAM;QAAW,MAAM;QAAM,OAAO;IAAG;IACzC;QAAE,MAAM;QAAY,MAAM;QAAK,OAAO;IAAI;CAC3C;AAEc,SAAS;;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,EAAE,SAAS,EAAE,cAAc,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,eAAY,AAAD;IAE7D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,YAAY;QACd;6BAAG,EAAE;IAEL,MAAM,iBAAiB,CAAC;QACtB,QAAQ,GAAG,CAAC,CAAC,cAAc,EAAE,QAAQ;IACrC,qDAAqD;IACvD;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,yIAAA,CAAA,UAAgB;;;;;0BAGjB,6LAAC,yIAAA,CAAA,UAAgB;gBACf,MAAK;gBACL,UAAS;gBACT,aAAa;gBACb,UAAU;gBACV,OAAO;;;;;;0BAIT,6LAAC;gBAAQ,WAAU;;kCACjB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAW,CAAC,yBAAyB,EAAE,WAAW,oBAAoB,aAAa;;0DACtF,6LAAC;gDAAG,WAAU;;kEACZ,6LAAC;wDAAK,WAAU;kEAAc;;;;;;kEAC9B,6LAAC;;;;;oDAAK;;;;;;;0DAGR,6LAAC;gDAAE,WAAU;0DAAsC;;;;;;0DAInD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,wIAAA,CAAA,UAAe;wDACd,aAAY;wDACZ,SAAQ;wDACR,MAAK;wDACL,SAAQ;wDACR,kBAAkB;4DAAE,QAAQ;wDAAgB;kEAC7C;;;;;;kEAGD,6LAAC,wIAAA,CAAA,UAAe;wDACd,aAAY;wDACZ,SAAQ;wDACR,MAAK;wDACL,SAAQ;wDACR,kBAAkB;4DAAE,QAAQ;wDAAgB;kEAC7C;;;;;;;;;;;;;;;;;;kDAKL,6LAAC;wCAAI,WAAW,CAAC,gBAAgB,EAAE,WAAW,qBAAqB,aAAa;kDAC9E,cAAA,6LAAC,uIAAA,CAAA,YAAS;4CACR,KAAI;4CACJ,KAAI;4CACJ,OAAO;4CACP,QAAQ;4CACR,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQpB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAI,WAAU;8DAAqC;;;;;;8DACpD,6LAAC;oDAAI,WAAU;8DAAwB;;;;;;;;;;;;sDAEzC,6LAAC;;8DACC,6LAAC;oDAAI,WAAU;8DAAqC;;;;;;8DACpD,6LAAC;oDAAI,WAAU;8DAAwB;;;;;;;;;;;;sDAEzC,6LAAC;;8DACC,6LAAC;oDAAI,WAAU;8DAAqC;;;;;;8DACpD,6LAAC;oDAAI,WAAU;8DAAwB;;;;;;;;;;;;sDAEzC,6LAAC;;8DACC,6LAAC;oDAAI,WAAU;8DAAqC;;;;;;8DACpD,6LAAC;oDAAI,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASnD,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAkD;;;;;;sCAGhE,6LAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC,UAAU,sBACzB,6LAAC;oCAEC,WAAW,CAAC,oHAAoH,EAC9H,WAAW,oBAAoB,aAC/B;oCACF,OAAO;wCAAE,gBAAgB,GAAG,QAAQ,IAAI,EAAE,CAAC;oCAAC;;sDAE5C,6LAAC;4CAAI,WAAU;sDAAiB,SAAS,IAAI;;;;;;sDAC7C,6LAAC;4CAAG,WAAU;sDAAiC,SAAS,IAAI;;;;;;sDAC5D,6LAAC;4CAAE,WAAU;;gDAAyB,SAAS,KAAK;gDAAC;;;;;;;;mCARhD,SAAS,IAAI;;;;;;;;;;;;;;;;;;;;;0BAgB5B,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAgC;;;;;;8CAG9C,6LAAC;oCAAO,WAAU;8CAAsD;;;;;;;;;;;;sCAK1E,6LAAC,iIAAA,CAAA,WAAQ;sCACN,cAAc,GAAG,CAAC,CAAC,MAAM,sBACxB,6LAAC;oCAEC,WAAW,GAAG,WAAW,oBAAoB,aAAa;oCAC1D,OAAO;wCAAE,gBAAgB,GAAG,QAAQ,IAAI,EAAE,CAAC;oCAAC;8CAE5C,cAAA,6LAAC,iIAAA,CAAA,UAAQ;wCACN,GAAG,IAAI;wCACR,QAAQ,IAAM,eAAe,KAAK,EAAE;wCACpC,YAAY,IAAM,eAAe,KAAK,EAAE;wCACxC,YAAY,WAAW,KAAK,EAAE;;;;;;mCAR3B,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;0BAiBtB,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAkD;;;;;;sCAGhE,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA0B;;;;;;sDACxC,6LAAC;4CAAE,WAAU;sDAAe;;;;;;sDAC5B,6LAAC,wIAAA,CAAA,UAAe;4CACd,aAAY;4CACZ,SAAQ;4CACR,MAAK;4CACL,SAAQ;4CACR,kBAAkB;gDAAE,OAAO;4CAAgB;4CAC3C,WAAU;sDACX;;;;;;;;;;;;8CAIH,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA0B;;;;;;sDACxC,6LAAC;4CAAE,WAAU;sDAAe;;;;;;sDAC5B,6LAAC,wIAAA,CAAA,UAAe;4CACd,aAAY;4CACZ,SAAQ;4CACR,MAAK;4CACL,SAAQ;4CACR,kBAAkB;gDAAE,OAAO;4CAAW;4CACtC,WAAU;sDACX;;;;;;;;;;;;8CAIH,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA0B;;;;;;sDACxC,6LAAC;4CAAE,WAAU;sDAAe;;;;;;sDAC5B,6LAAC,wIAAA,CAAA,UAAe;4CACd,aAAY;4CACZ,SAAQ;4CACR,MAAK;4CACL,SAAQ;4CACR,kBAAkB;gDAAE,OAAO;4CAAa;4CACxC,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAST,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAoC;;;;;;sDAClD,6LAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;8CAIvC,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAgC;;;;;;sDAC9C,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;8DAAG,cAAA,6LAAC;wDAAE,MAAK;wDAAS,WAAU;kEAAmB;;;;;;;;;;;8DAClD,6LAAC;8DAAG,cAAA,6LAAC;wDAAE,MAAK;wDAAQ,WAAU;kEAAmB;;;;;;;;;;;8DACjD,6LAAC;8DAAG,cAAA,6LAAC;wDAAE,MAAK;wDAAW,WAAU;kEAAmB;;;;;;;;;;;8DACpD,6LAAC;8DAAG,cAAA,6LAAC;wDAAE,MAAK;wDAAY,WAAU;kEAAmB;;;;;;;;;;;;;;;;;;;;;;;8CAGzD,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAgC;;;;;;sDAC9C,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;8DAAG,cAAA,6LAAC;wDAAE,MAAK;wDAAS,WAAU;kEAAmB;;;;;;;;;;;8DAClD,6LAAC;8DAAG,cAAA,6LAAC;wDAAE,MAAK;wDAAW,WAAU;kEAAmB;;;;;;;;;;;8DACpD,6LAAC;8DAAG,cAAA,6LAAC;wDAAE,MAAK;wDAAQ,WAAU;kEAAmB;;;;;;;;;;;;;;;;;;;;;;;8CAGrD,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAgC;;;;;;sDAC9C,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;8DAAG,cAAA,6LAAC;wDAAE,MAAK;wDAAU,WAAU;kEAAmB;;;;;;;;;;;8DACnD,6LAAC;8DAAG,cAAA,6LAAC;wDAAE,MAAK;wDAAe,WAAU;kEAAmB;;;;;;;;;;;8DACxD,6LAAC;8DAAG,cAAA,6LAAC;wDAAE,MAAK;wDAAe,WAAU;kEAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAI9D,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;0CAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMf;GA7PwB;;QAE4B,iIAAA,CAAA,eAAY;;;KAFxC", "debugId": null}}]}