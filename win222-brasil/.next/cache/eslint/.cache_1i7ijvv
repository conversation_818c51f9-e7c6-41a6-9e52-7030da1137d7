[{"/home/<USER>/文档/win222/win222-brasil/src/app/api/analytics/conversion/route.ts": "1", "/home/<USER>/文档/win222/win222-brasil/src/app/api/analytics/web-vitals/route.ts": "2", "/home/<USER>/文档/win222/win222-brasil/src/app/cassino/page.tsx": "3", "/home/<USER>/文档/win222/win222-brasil/src/app/jogos/page.tsx": "4", "/home/<USER>/文档/win222/win222-brasil/src/app/layout.tsx": "5", "/home/<USER>/文档/win222/win222-brasil/src/app/page.tsx": "6", "/home/<USER>/文档/win222/win222-brasil/src/app/sitemap-images.xml/route.ts": "7", "/home/<USER>/文档/win222/win222-brasil/src/app/sitemap.ts": "8", "/home/<USER>/文档/win222/win222-brasil/src/app/sobre/page.tsx": "9", "/home/<USER>/文档/win222/win222-brasil/src/components/ABTestProvider.tsx": "10", "/home/<USER>/文档/win222/win222-brasil/src/components/AffiliateButton.tsx": "11", "/home/<USER>/文档/win222/win222-brasil/src/components/ConversionBanner.tsx": "12", "/home/<USER>/文档/win222/win222-brasil/src/components/GameCard.tsx": "13", "/home/<USER>/文档/win222/win222-brasil/src/components/HomePage.tsx": "14", "/home/<USER>/文档/win222/win222-brasil/src/components/MobileNavigation.tsx": "15", "/home/<USER>/文档/win222/win222-brasil/src/components/OptimizedImage.tsx": "16", "/home/<USER>/文档/win222/win222-brasil/src/components/StructuredData.tsx": "17", "/home/<USER>/文档/win222/win222-brasil/src/components/WebVitals.tsx": "18", "/home/<USER>/文档/win222/win222-brasil/src/config/seo.ts": "19", "/home/<USER>/文档/win222/win222-brasil/src/lib/affiliate.ts": "20", "/home/<USER>/文档/win222/win222-brasil/src/components/Breadcrumb.tsx": "21", "/home/<USER>/文档/win222/win222-brasil/src/components/InternalLinkNetwork.tsx": "22", "/home/<USER>/文档/win222/win222-brasil/src/components/RelatedContent.tsx": "23", "/home/<USER>/文档/win222/win222-brasil/src/app/cidades/page.tsx": "24", "/home/<USER>/文档/win222/win222-brasil/src/app/faq/FAQClient.tsx": "25", "/home/<USER>/文档/win222/win222-brasil/src/app/faq/page.tsx": "26", "/home/<USER>/文档/win222/win222-brasil/src/app/glossario/page.tsx": "27", "/home/<USER>/文档/win222/win222-brasil/src/app/guias/como-jogar-slots/page.tsx": "28", "/home/<USER>/文档/win222/win222-brasil/src/app/guias/page.tsx": "29", "/home/<USER>/文档/win222/win222-brasil/src/components/AdvancedSEO.tsx": "30", "/home/<USER>/文档/win222/win222-brasil/src/components/CriticalResourceHints.tsx": "31", "/home/<USER>/文档/win222/win222-brasil/src/components/PerformanceOptimizer.tsx": "32", "/home/<USER>/文档/win222/win222-brasil/src/components/SemanticHTML.tsx": "33", "/home/<USER>/文档/win222/win222-brasil/src/middleware.ts": "34"}, {"size": 5160, "mtime": 1750953919827, "results": "35", "hashOfConfig": "36"}, {"size": 4653, "mtime": 1750953991965, "results": "37", "hashOfConfig": "36"}, {"size": 23437, "mtime": 1750957499531, "results": "38", "hashOfConfig": "36"}, {"size": 17890, "mtime": 1750957027342, "results": "39", "hashOfConfig": "36"}, {"size": 4581, "mtime": 1750959285753, "results": "40", "hashOfConfig": "36"}, {"size": 456, "mtime": 1750959313135, "results": "41", "hashOfConfig": "36"}, {"size": 5003, "mtime": 1750947716334, "results": "42", "hashOfConfig": "36"}, {"size": 4112, "mtime": 1750958112104, "results": "43", "hashOfConfig": "36"}, {"size": 25477, "mtime": 1750957550350, "results": "44", "hashOfConfig": "36"}, {"size": 6308, "mtime": 1750953604290, "results": "45", "hashOfConfig": "36"}, {"size": 4105, "mtime": 1750948719587, "results": "46", "hashOfConfig": "36"}, {"size": 5852, "mtime": 1750948756236, "results": "47", "hashOfConfig": "36"}, {"size": 8792, "mtime": 1750948844278, "results": "48", "hashOfConfig": "36"}, {"size": 36034, "mtime": 1750956941641, "results": "49", "hashOfConfig": "36"}, {"size": 9692, "mtime": 1750948152891, "results": "50", "hashOfConfig": "36"}, {"size": 6604, "mtime": 1750953676129, "results": "51", "hashOfConfig": "36"}, {"size": 11281, "mtime": 1750958696965, "results": "52", "hashOfConfig": "36"}, {"size": 6244, "mtime": 1750954089314, "results": "53", "hashOfConfig": "36"}, {"size": 5013, "mtime": 1750947260841, "results": "54", "hashOfConfig": "36"}, {"size": 7096, "mtime": 1750954128808, "results": "55", "hashOfConfig": "36"}, {"size": 8304, "mtime": 1750956824198, "results": "56", "hashOfConfig": "36"}, {"size": 10912, "mtime": 1750957601829, "results": "57", "hashOfConfig": "36"}, {"size": 10017, "mtime": 1750957624246, "results": "58", "hashOfConfig": "36"}, {"size": 17571, "mtime": 1750958302447, "results": "59", "hashOfConfig": "36"}, {"size": 8038, "mtime": 1750958312808, "results": "60", "hashOfConfig": "36"}, {"size": 5729, "mtime": 1750958323699, "results": "61", "hashOfConfig": "36"}, {"size": 14667, "mtime": 1750958333818, "results": "62", "hashOfConfig": "36"}, {"size": 13938, "mtime": 1750958360791, "results": "63", "hashOfConfig": "36"}, {"size": 11626, "mtime": 1750958387696, "results": "64", "hashOfConfig": "36"}, {"size": 9981, "mtime": 1750958994669, "results": "65", "hashOfConfig": "36"}, {"size": 1374, "mtime": 1750958909702, "results": "66", "hashOfConfig": "36"}, {"size": 10320, "mtime": 1750959019814, "results": "67", "hashOfConfig": "36"}, {"size": 7407, "mtime": 1750959092336, "results": "68", "hashOfConfig": "36"}, {"size": 5415, "mtime": 1750959122211, "results": "69", "hashOfConfig": "36"}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "ldqxvl", {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/home/<USER>/文档/win222/win222-brasil/src/app/api/analytics/conversion/route.ts", [], [], "/home/<USER>/文档/win222/win222-brasil/src/app/api/analytics/web-vitals/route.ts", [], [], "/home/<USER>/文档/win222/win222-brasil/src/app/cassino/page.tsx", [], [], "/home/<USER>/文档/win222/win222-brasil/src/app/jogos/page.tsx", [], [], "/home/<USER>/文档/win222/win222-brasil/src/app/layout.tsx", [], [], "/home/<USER>/文档/win222/win222-brasil/src/app/page.tsx", [], [], "/home/<USER>/文档/win222/win222-brasil/src/app/sitemap-images.xml/route.ts", [], [], "/home/<USER>/文档/win222/win222-brasil/src/app/sitemap.ts", [], [], "/home/<USER>/文档/win222/win222-brasil/src/app/sobre/page.tsx", [], [], "/home/<USER>/文档/win222/win222-brasil/src/components/ABTestProvider.tsx", [], [], "/home/<USER>/文档/win222/win222-brasil/src/components/AffiliateButton.tsx", [], [], "/home/<USER>/文档/win222/win222-brasil/src/components/ConversionBanner.tsx", [], [], "/home/<USER>/文档/win222/win222-brasil/src/components/GameCard.tsx", [], [], "/home/<USER>/文档/win222/win222-brasil/src/components/HomePage.tsx", [], [], "/home/<USER>/文档/win222/win222-brasil/src/components/MobileNavigation.tsx", [], [], "/home/<USER>/文档/win222/win222-brasil/src/components/OptimizedImage.tsx", [], [], "/home/<USER>/文档/win222/win222-brasil/src/components/StructuredData.tsx", [], [], "/home/<USER>/文档/win222/win222-brasil/src/components/WebVitals.tsx", [], [], "/home/<USER>/文档/win222/win222-brasil/src/config/seo.ts", [], [], "/home/<USER>/文档/win222/win222-brasil/src/lib/affiliate.ts", [], [], "/home/<USER>/文档/win222/win222-brasil/src/components/Breadcrumb.tsx", [], [], "/home/<USER>/文档/win222/win222-brasil/src/components/InternalLinkNetwork.tsx", [], [], "/home/<USER>/文档/win222/win222-brasil/src/components/RelatedContent.tsx", [], [], "/home/<USER>/文档/win222/win222-brasil/src/app/cidades/page.tsx", [], [], "/home/<USER>/文档/win222/win222-brasil/src/app/faq/FAQClient.tsx", [], [], "/home/<USER>/文档/win222/win222-brasil/src/app/faq/page.tsx", [], [], "/home/<USER>/文档/win222/win222-brasil/src/app/glossario/page.tsx", [], [], "/home/<USER>/文档/win222/win222-brasil/src/app/guias/como-jogar-slots/page.tsx", [], [], "/home/<USER>/文档/win222/win222-brasil/src/app/guias/page.tsx", [], [], "/home/<USER>/文档/win222/win222-brasil/src/components/AdvancedSEO.tsx", [], [], "/home/<USER>/文档/win222/win222-brasil/src/components/CriticalResourceHints.tsx", [], [], "/home/<USER>/文档/win222/win222-brasil/src/components/PerformanceOptimizer.tsx", ["172"], [], "/home/<USER>/文档/win222/win222-brasil/src/components/SemanticHTML.tsx", [], [], "/home/<USER>/文档/win222/win222-brasil/src/middleware.ts", [], [], {"ruleId": "173", "severity": 1, "message": "174", "line": 263, "column": 38, "nodeType": "175", "endLine": 263, "endColumn": 45}, "react-hooks/exhaustive-deps", "The ref value 'observersRef.current' will likely have changed by the time this effect cleanup function runs. If this ref points to a node rendered by React, copy 'observersRef.current' to a variable inside the effect, and use that variable in the cleanup function.", "Identifier"]