[{"/home/<USER>/文档/win222/win222-brasil/src/app/api/analytics/conversion/route.ts": "1", "/home/<USER>/文档/win222/win222-brasil/src/app/api/analytics/web-vitals/route.ts": "2", "/home/<USER>/文档/win222/win222-brasil/src/app/cassino/page.tsx": "3", "/home/<USER>/文档/win222/win222-brasil/src/app/jogos/page.tsx": "4", "/home/<USER>/文档/win222/win222-brasil/src/app/layout.tsx": "5", "/home/<USER>/文档/win222/win222-brasil/src/app/page.tsx": "6", "/home/<USER>/文档/win222/win222-brasil/src/app/sitemap-images.xml/route.ts": "7", "/home/<USER>/文档/win222/win222-brasil/src/app/sobre/page.tsx": "8", "/home/<USER>/文档/win222/win222-brasil/src/components/ABTestProvider.tsx": "9", "/home/<USER>/文档/win222/win222-brasil/src/components/AffiliateButton.tsx": "10", "/home/<USER>/文档/win222/win222-brasil/src/components/ConversionBanner.tsx": "11", "/home/<USER>/文档/win222/win222-brasil/src/components/GameCard.tsx": "12", "/home/<USER>/文档/win222/win222-brasil/src/components/HomePage.tsx": "13", "/home/<USER>/文档/win222/win222-brasil/src/components/MobileNavigation.tsx": "14", "/home/<USER>/文档/win222/win222-brasil/src/components/OptimizedImage.tsx": "15", "/home/<USER>/文档/win222/win222-brasil/src/components/StructuredData.tsx": "16", "/home/<USER>/文档/win222/win222-brasil/src/components/WebVitals.tsx": "17", "/home/<USER>/文档/win222/win222-brasil/src/config/seo.ts": "18", "/home/<USER>/文档/win222/win222-brasil/src/lib/affiliate.ts": "19", "/home/<USER>/文档/win222/win222-brasil/src/components/Breadcrumb.tsx": "20", "/home/<USER>/文档/win222/win222-brasil/src/components/InternalLinkNetwork.tsx": "21", "/home/<USER>/文档/win222/win222-brasil/src/components/RelatedContent.tsx": "22", "/home/<USER>/文档/win222/win222-brasil/src/app/cidades/page.tsx": "23", "/home/<USER>/文档/win222/win222-brasil/src/app/faq/FAQClient.tsx": "24", "/home/<USER>/文档/win222/win222-brasil/src/app/faq/page.tsx": "25", "/home/<USER>/文档/win222/win222-brasil/src/app/glossario/page.tsx": "26", "/home/<USER>/文档/win222/win222-brasil/src/app/guias/como-jogar-slots/page.tsx": "27", "/home/<USER>/文档/win222/win222-brasil/src/app/guias/page.tsx": "28", "/home/<USER>/文档/win222/win222-brasil/src/components/AdvancedSEO.tsx": "29", "/home/<USER>/文档/win222/win222-brasil/src/components/CriticalResourceHints.tsx": "30", "/home/<USER>/文档/win222/win222-brasil/src/components/PerformanceOptimizer.tsx": "31", "/home/<USER>/文档/win222/win222-brasil/src/components/SemanticHTML.tsx": "32", "/home/<USER>/文档/win222/win222-brasil/src/middleware.ts": "33"}, {"size": 5160, "mtime": 1750953919827, "results": "34", "hashOfConfig": "35"}, {"size": 4653, "mtime": 1750953991965, "results": "36", "hashOfConfig": "35"}, {"size": 23437, "mtime": 1750957499531, "results": "37", "hashOfConfig": "35"}, {"size": 17890, "mtime": 1750957027342, "results": "38", "hashOfConfig": "35"}, {"size": 4589, "mtime": 1750960452455, "results": "39", "hashOfConfig": "35"}, {"size": 349, "mtime": 1750960587550, "results": "40", "hashOfConfig": "35"}, {"size": 5003, "mtime": 1750947716334, "results": "41", "hashOfConfig": "35"}, {"size": 25477, "mtime": 1750957550350, "results": "42", "hashOfConfig": "35"}, {"size": 6308, "mtime": 1750953604290, "results": "43", "hashOfConfig": "35"}, {"size": 4105, "mtime": 1750948719587, "results": "44", "hashOfConfig": "35"}, {"size": 5852, "mtime": 1750948756236, "results": "45", "hashOfConfig": "35"}, {"size": 8792, "mtime": 1750948844278, "results": "46", "hashOfConfig": "35"}, {"size": 36034, "mtime": 1750956941641, "results": "47", "hashOfConfig": "35"}, {"size": 9692, "mtime": 1750948152891, "results": "48", "hashOfConfig": "35"}, {"size": 6604, "mtime": 1750953676129, "results": "49", "hashOfConfig": "35"}, {"size": 11281, "mtime": 1750958696965, "results": "50", "hashOfConfig": "35"}, {"size": 7181, "mtime": 1750960191456, "results": "51", "hashOfConfig": "35"}, {"size": 5013, "mtime": 1750947260841, "results": "52", "hashOfConfig": "35"}, {"size": 7096, "mtime": 1750954128808, "results": "53", "hashOfConfig": "35"}, {"size": 8304, "mtime": 1750956824198, "results": "54", "hashOfConfig": "35"}, {"size": 10912, "mtime": 1750957601829, "results": "55", "hashOfConfig": "35"}, {"size": 10017, "mtime": 1750957624246, "results": "56", "hashOfConfig": "35"}, {"size": 17571, "mtime": 1750958302447, "results": "57", "hashOfConfig": "35"}, {"size": 8038, "mtime": 1750958312808, "results": "58", "hashOfConfig": "35"}, {"size": 5729, "mtime": 1750958323699, "results": "59", "hashOfConfig": "35"}, {"size": 14667, "mtime": 1750958333818, "results": "60", "hashOfConfig": "35"}, {"size": 13938, "mtime": 1750958360791, "results": "61", "hashOfConfig": "35"}, {"size": 11626, "mtime": 1750958387696, "results": "62", "hashOfConfig": "35"}, {"size": 9981, "mtime": 1750958994669, "results": "63", "hashOfConfig": "35"}, {"size": 1374, "mtime": 1750958909702, "results": "64", "hashOfConfig": "35"}, {"size": 10355, "mtime": 1750960079966, "results": "65", "hashOfConfig": "35"}, {"size": 7407, "mtime": 1750959092336, "results": "66", "hashOfConfig": "35"}, {"size": 5415, "mtime": 1750959122211, "results": "67", "hashOfConfig": "35"}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "ldqxvl", {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "158", "messages": "159", "suppressedMessages": "160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "161", "messages": "162", "suppressedMessages": "163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/home/<USER>/文档/win222/win222-brasil/src/app/api/analytics/conversion/route.ts", [], [], "/home/<USER>/文档/win222/win222-brasil/src/app/api/analytics/web-vitals/route.ts", [], [], "/home/<USER>/文档/win222/win222-brasil/src/app/cassino/page.tsx", [], [], "/home/<USER>/文档/win222/win222-brasil/src/app/jogos/page.tsx", [], [], "/home/<USER>/文档/win222/win222-brasil/src/app/layout.tsx", [], [], "/home/<USER>/文档/win222/win222-brasil/src/app/page.tsx", [], [], "/home/<USER>/文档/win222/win222-brasil/src/app/sitemap-images.xml/route.ts", [], [], "/home/<USER>/文档/win222/win222-brasil/src/app/sobre/page.tsx", [], [], "/home/<USER>/文档/win222/win222-brasil/src/components/ABTestProvider.tsx", [], [], "/home/<USER>/文档/win222/win222-brasil/src/components/AffiliateButton.tsx", [], [], "/home/<USER>/文档/win222/win222-brasil/src/components/ConversionBanner.tsx", [], [], "/home/<USER>/文档/win222/win222-brasil/src/components/GameCard.tsx", [], [], "/home/<USER>/文档/win222/win222-brasil/src/components/HomePage.tsx", [], [], "/home/<USER>/文档/win222/win222-brasil/src/components/MobileNavigation.tsx", [], [], "/home/<USER>/文档/win222/win222-brasil/src/components/OptimizedImage.tsx", [], [], "/home/<USER>/文档/win222/win222-brasil/src/components/StructuredData.tsx", [], [], "/home/<USER>/文档/win222/win222-brasil/src/components/WebVitals.tsx", [], [], "/home/<USER>/文档/win222/win222-brasil/src/config/seo.ts", [], [], "/home/<USER>/文档/win222/win222-brasil/src/lib/affiliate.ts", [], [], "/home/<USER>/文档/win222/win222-brasil/src/components/Breadcrumb.tsx", [], [], "/home/<USER>/文档/win222/win222-brasil/src/components/InternalLinkNetwork.tsx", [], [], "/home/<USER>/文档/win222/win222-brasil/src/components/RelatedContent.tsx", [], [], "/home/<USER>/文档/win222/win222-brasil/src/app/cidades/page.tsx", [], [], "/home/<USER>/文档/win222/win222-brasil/src/app/faq/FAQClient.tsx", [], [], "/home/<USER>/文档/win222/win222-brasil/src/app/faq/page.tsx", [], [], "/home/<USER>/文档/win222/win222-brasil/src/app/glossario/page.tsx", [], [], "/home/<USER>/文档/win222/win222-brasil/src/app/guias/como-jogar-slots/page.tsx", [], [], "/home/<USER>/文档/win222/win222-brasil/src/app/guias/page.tsx", [], [], "/home/<USER>/文档/win222/win222-brasil/src/components/AdvancedSEO.tsx", [], [], "/home/<USER>/文档/win222/win222-brasil/src/components/CriticalResourceHints.tsx", [], [], "/home/<USER>/文档/win222/win222-brasil/src/components/PerformanceOptimizer.tsx", ["167"], [], "/home/<USER>/文档/win222/win222-brasil/src/components/SemanticHTML.tsx", [], [], "/home/<USER>/文档/win222/win222-brasil/src/middleware.ts", [], [], {"ruleId": "168", "severity": 1, "message": "169", "line": 263, "column": 45, "nodeType": "170", "endLine": 263, "endColumn": 52}, "react-hooks/exhaustive-deps", "The ref value 'observersRef.current' will likely have changed by the time this effect cleanup function runs. If this ref points to a node rendered by React, copy 'observersRef.current' to a variable inside the effect, and use that variable in the cleanup function.", "Identifier"]