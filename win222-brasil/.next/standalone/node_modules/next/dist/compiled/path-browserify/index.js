(function(){"use strict";var e={114:function(e){function assertPath(e){if(typeof e!=="string"){throw new TypeError("Path must be a string. Received "+JSON.stringify(e))}}function normalizeStringPosix(e,r){var t="";var i=0;var n=-1;var a=0;var f;for(var l=0;l<=e.length;++l){if(l<e.length)f=e.charCodeAt(l);else if(f===47)break;else f=47;if(f===47){if(n===l-1||a===1){}else if(n!==l-1&&a===2){if(t.length<2||i!==2||t.charCodeAt(t.length-1)!==46||t.charCodeAt(t.length-2)!==46){if(t.length>2){var s=t.lastIndexOf("/");if(s!==t.length-1){if(s===-1){t="";i=0}else{t=t.slice(0,s);i=t.length-1-t.lastIndexOf("/")}n=l;a=0;continue}}else if(t.length===2||t.length===1){t="";i=0;n=l;a=0;continue}}if(r){if(t.length>0)t+="/..";else t="..";i=2}}else{if(t.length>0)t+="/"+e.slice(n+1,l);else t=e.slice(n+1,l);i=l-n-1}n=l;a=0}else if(f===46&&a!==-1){++a}else{a=-1}}return t}function _format(e,r){var t=r.dir||r.root;var i=r.base||(r.name||"")+(r.ext||"");if(!t){return i}if(t===r.root){return t+i}return t+e+i}var r={resolve:function resolve(){var e="";var r=false;var t;for(var i=arguments.length-1;i>=-1&&!r;i--){var n;if(i>=0)n=arguments[i];else{if(t===undefined)t="";n=t}assertPath(n);if(n.length===0){continue}e=n+"/"+e;r=n.charCodeAt(0)===47}e=normalizeStringPosix(e,!r);if(r){if(e.length>0)return"/"+e;else return"/"}else if(e.length>0){return e}else{return"."}},normalize:function normalize(e){assertPath(e);if(e.length===0)return".";var r=e.charCodeAt(0)===47;var t=e.charCodeAt(e.length-1)===47;e=normalizeStringPosix(e,!r);if(e.length===0&&!r)e=".";if(e.length>0&&t)e+="/";if(r)return"/"+e;return e},isAbsolute:function isAbsolute(e){assertPath(e);return e.length>0&&e.charCodeAt(0)===47},join:function join(){if(arguments.length===0)return".";var e;for(var t=0;t<arguments.length;++t){var i=arguments[t];assertPath(i);if(i.length>0){if(e===undefined)e=i;else e+="/"+i}}if(e===undefined)return".";return r.normalize(e)},relative:function relative(e,t){assertPath(e);assertPath(t);if(e===t)return"";e=r.resolve(e);t=r.resolve(t);if(e===t)return"";var i=1;for(;i<e.length;++i){if(e.charCodeAt(i)!==47)break}var n=e.length;var a=n-i;var f=1;for(;f<t.length;++f){if(t.charCodeAt(f)!==47)break}var l=t.length;var s=l-f;var o=a<s?a:s;var u=-1;var h=0;for(;h<=o;++h){if(h===o){if(s>o){if(t.charCodeAt(f+h)===47){return t.slice(f+h+1)}else if(h===0){return t.slice(f+h)}}else if(a>o){if(e.charCodeAt(i+h)===47){u=h}else if(h===0){u=0}}break}var c=e.charCodeAt(i+h);var v=t.charCodeAt(f+h);if(c!==v)break;else if(c===47)u=h}var g="";for(h=i+u+1;h<=n;++h){if(h===n||e.charCodeAt(h)===47){if(g.length===0)g+="..";else g+="/.."}}if(g.length>0)return g+t.slice(f+u);else{f+=u;if(t.charCodeAt(f)===47)++f;return t.slice(f)}},_makeLong:function _makeLong(e){return e},dirname:function dirname(e){assertPath(e);if(e.length===0)return".";var r=e.charCodeAt(0);var t=r===47;var i=-1;var n=true;for(var a=e.length-1;a>=1;--a){r=e.charCodeAt(a);if(r===47){if(!n){i=a;break}}else{n=false}}if(i===-1)return t?"/":".";if(t&&i===1)return"//";return e.slice(0,i)},basename:function basename(e,r){if(r!==undefined&&typeof r!=="string")throw new TypeError('"ext" argument must be a string');assertPath(e);var t=0;var i=-1;var n=true;var a;if(r!==undefined&&r.length>0&&r.length<=e.length){if(r.length===e.length&&r===e)return"";var f=r.length-1;var l=-1;for(a=e.length-1;a>=0;--a){var s=e.charCodeAt(a);if(s===47){if(!n){t=a+1;break}}else{if(l===-1){n=false;l=a+1}if(f>=0){if(s===r.charCodeAt(f)){if(--f===-1){i=a}}else{f=-1;i=l}}}}if(t===i)i=l;else if(i===-1)i=e.length;return e.slice(t,i)}else{for(a=e.length-1;a>=0;--a){if(e.charCodeAt(a)===47){if(!n){t=a+1;break}}else if(i===-1){n=false;i=a+1}}if(i===-1)return"";return e.slice(t,i)}},extname:function extname(e){assertPath(e);var r=-1;var t=0;var i=-1;var n=true;var a=0;for(var f=e.length-1;f>=0;--f){var l=e.charCodeAt(f);if(l===47){if(!n){t=f+1;break}continue}if(i===-1){n=false;i=f+1}if(l===46){if(r===-1)r=f;else if(a!==1)a=1}else if(r!==-1){a=-1}}if(r===-1||i===-1||a===0||a===1&&r===i-1&&r===t+1){return""}return e.slice(r,i)},format:function format(e){if(e===null||typeof e!=="object"){throw new TypeError('The "pathObject" argument must be of type Object. Received type '+typeof e)}return _format("/",e)},parse:function parse(e){assertPath(e);var r={root:"",dir:"",base:"",ext:"",name:""};if(e.length===0)return r;var t=e.charCodeAt(0);var i=t===47;var n;if(i){r.root="/";n=1}else{n=0}var a=-1;var f=0;var l=-1;var s=true;var o=e.length-1;var u=0;for(;o>=n;--o){t=e.charCodeAt(o);if(t===47){if(!s){f=o+1;break}continue}if(l===-1){s=false;l=o+1}if(t===46){if(a===-1)a=o;else if(u!==1)u=1}else if(a!==-1){u=-1}}if(a===-1||l===-1||u===0||u===1&&a===l-1&&a===f+1){if(l!==-1){if(f===0&&i)r.base=r.name=e.slice(1,l);else r.base=r.name=e.slice(f,l)}}else{if(f===0&&i){r.name=e.slice(1,a);r.base=e.slice(1,l)}else{r.name=e.slice(f,a);r.base=e.slice(f,l)}r.ext=e.slice(a,l)}if(f>0)r.dir=e.slice(0,f-1);else if(i)r.dir="/";return r},sep:"/",delimiter:":",win32:null,posix:null};r.posix=r;e.exports=r}};var r={};function __nccwpck_require__(t){var i=r[t];if(i!==undefined){return i.exports}var n=r[t]={exports:{}};var a=true;try{e[t](n,n.exports,__nccwpck_require__);a=false}finally{if(a)delete r[t]}return n.exports}if(typeof __nccwpck_require__!=="undefined")__nccwpck_require__.ab=__dirname+"/";var t=__nccwpck_require__(114);module.exports=t})();