/**
 * Affiliate Marketing Management System
 * Handles affiliate links, tracking, and conversion optimization
 */

export interface AffiliateLink {
  id: string;
  name: string;
  url: string;
  category: 'casino' | 'sports' | 'poker' | 'slots' | 'live' | 'bonus';
  priority: number;
  isActive: boolean;
  trackingParams?: Record<string, string>;
  conversionGoals?: string[];
}

export interface ConversionEvent {
  type: 'click' | 'signup' | 'deposit' | 'play' | 'view';
  affiliateId: string;
  userId?: string;
  value?: number;
  metadata?: Record<string, any>;
}

// Affiliate links configuration
export const AFFILIATE_LINKS: Record<string, AffiliateLink> = {
  // Main Casino Platform
  main_casino: {
    id: 'main_casino',
    name: 'Win222 Brasil - Cassino Principal',
    url: 'https://win222brasil.com/register',
    category: 'casino',
    priority: 1,
    isActive: true,
    trackingParams: {
      utm_source: 'win222brasil',
      utm_medium: 'website',
      utm_campaign: 'main_casino',
      ref: 'win222br'
    },
    conversionGoals: ['signup', 'deposit']
  },

  // Sports Betting
  sports_betting: {
    id: 'sports_betting',
    name: 'Win222 Brasil - Apostas Esportivas',
    url: 'https://win222brasil.com/sports',
    category: 'sports',
    priority: 1,
    isActive: true,
    trackingParams: {
      utm_source: 'win222brasil',
      utm_medium: 'website',
      utm_campaign: 'sports_betting',
      ref: 'win222br_sports'
    },
    conversionGoals: ['signup', 'bet']
  },

  // Slots Games
  slots_games: {
    id: 'slots_games',
    name: 'Win222 Brasil - Slots Premium',
    url: 'https://win222brasil.com/slots',
    category: 'slots',
    priority: 2,
    isActive: true,
    trackingParams: {
      utm_source: 'win222brasil',
      utm_medium: 'website',
      utm_campaign: 'slots_games',
      ref: 'win222br_slots'
    },
    conversionGoals: ['play', 'deposit']
  },

  // Live Casino
  live_casino: {
    id: 'live_casino',
    name: 'Win222 Brasil - Cassino Ao Vivo',
    url: 'https://win222brasil.com/live',
    category: 'live',
    priority: 2,
    isActive: true,
    trackingParams: {
      utm_source: 'win222brasil',
      utm_medium: 'website',
      utm_campaign: 'live_casino',
      ref: 'win222br_live'
    },
    conversionGoals: ['play', 'deposit']
  },

  // Welcome Bonus
  welcome_bonus: {
    id: 'welcome_bonus',
    name: 'Win222 Brasil - Bônus de Boas-vindas',
    url: 'https://win222brasil.com/bonus',
    category: 'bonus',
    priority: 1,
    isActive: true,
    trackingParams: {
      utm_source: 'win222brasil',
      utm_medium: 'website',
      utm_campaign: 'welcome_bonus',
      ref: 'win222br_bonus'
    },
    conversionGoals: ['signup', 'claim_bonus']
  },

  // Poker Games
  poker_games: {
    id: 'poker_games',
    name: 'Win222 Brasil - Poker Online',
    url: 'https://win222brasil.com/poker',
    category: 'poker',
    priority: 3,
    isActive: true,
    trackingParams: {
      utm_source: 'win222brasil',
      utm_medium: 'website',
      utm_campaign: 'poker_games',
      ref: 'win222br_poker'
    },
    conversionGoals: ['play', 'tournament']
  }
};

/**
 * Generate affiliate URL with tracking parameters
 */
export function generateAffiliateUrl(
  affiliateId: string,
  additionalParams?: Record<string, string>
): string {
  const affiliate = AFFILIATE_LINKS[affiliateId];
  if (!affiliate || !affiliate.isActive) {
    return '#';
  }

  const url = new URL(affiliate.url);
  
  // Add tracking parameters
  if (affiliate.trackingParams) {
    Object.entries(affiliate.trackingParams).forEach(([key, value]) => {
      url.searchParams.set(key, value);
    });
  }

  // Add additional parameters
  if (additionalParams) {
    Object.entries(additionalParams).forEach(([key, value]) => {
      url.searchParams.set(key, value);
    });
  }

  // Add timestamp for unique tracking
  url.searchParams.set('t', Date.now().toString());

  return url.toString();
}

/**
 * Track conversion events
 */
export function trackConversion(event: ConversionEvent): void {
  // Send to analytics
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', 'conversion', {
      event_category: 'affiliate',
      event_label: event.affiliateId,
      value: event.value || 0,
      custom_parameters: {
        conversion_type: event.type,
        affiliate_id: event.affiliateId,
        ...event.metadata
      }
    });
  }

  // Send to custom analytics endpoint
  if (typeof window !== 'undefined') {
    fetch('/api/analytics/conversion', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        ...event,
        timestamp: new Date().toISOString(),
        userAgent: navigator.userAgent,
        referrer: document.referrer,
        url: window.location.href
      })
    }).catch(console.error);
  }
}

/**
 * Get affiliate links by category
 */
export function getAffiliatesByCategory(category: AffiliateLink['category']): AffiliateLink[] {
  return Object.values(AFFILIATE_LINKS)
    .filter(link => link.category === category && link.isActive)
    .sort((a, b) => a.priority - b.priority);
}

/**
 * Get primary affiliate link for a category
 */
export function getPrimaryAffiliate(category: AffiliateLink['category']): AffiliateLink | null {
  const affiliates = getAffiliatesByCategory(category);
  return affiliates.length > 0 ? affiliates[0] : null;
}

/**
 * A/B test affiliate links
 */
export function getABTestAffiliate(category: AffiliateLink['category']): AffiliateLink | null {
  const affiliates = getAffiliatesByCategory(category);
  if (affiliates.length === 0) return null;
  
  // Simple A/B testing based on user session
  const sessionId = typeof window !== 'undefined' 
    ? sessionStorage.getItem('ab_test_id') || Math.random().toString(36)
    : Math.random().toString(36);
  
  if (typeof window !== 'undefined') {
    sessionStorage.setItem('ab_test_id', sessionId);
  }
  
  const hash = sessionId.split('').reduce((a, b) => {
    a = ((a << 5) - a) + b.charCodeAt(0);
    return a & a;
  }, 0);
  
  const index = Math.abs(hash) % affiliates.length;
  return affiliates[index];
}

/**
 * Track affiliate link clicks
 */
export function trackAffiliateClick(affiliateId: string, context?: string): void {
  trackConversion({
    type: 'click',
    affiliateId,
    metadata: { context }
  });
}

/**
 * SEO-friendly affiliate link wrapper
 */
export function createSEOFriendlyLink(
  affiliateId: string,
  text: string,
  className?: string,
  additionalParams?: Record<string, string>
): {
  href: string;
  onClick: () => void;
  rel: string;
  target: string;
  className?: string;
  children: string;
} {
  const url = generateAffiliateUrl(affiliateId, additionalParams);
  
  return {
    href: url,
    onClick: () => trackAffiliateClick(affiliateId),
    rel: 'noopener noreferrer sponsored',
    target: '_blank',
    className,
    children: text
  };
}
