import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

export function middleware(request: NextRequest) {
  const response = NextResponse.next();
  const { pathname } = request.nextUrl;

  // Security headers
  response.headers.set('X-Frame-Options', 'DENY');
  response.headers.set('X-Content-Type-Options', 'nosniff');
  response.headers.set('Referrer-Policy', 'origin-when-cross-origin');
  response.headers.set('X-XSS-Protection', '1; mode=block');
  response.headers.set('Permissions-Policy', 'camera=(), microphone=(), geolocation=()');

  // Content Security Policy
  const csp = [
    "default-src 'self'",
    "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://www.google-analytics.com https://www.googletagmanager.com https://connect.facebook.net",
    "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com",
    "font-src 'self' https://fonts.gstatic.com",
    "img-src 'self' data: https: blob:",
    "connect-src 'self' https://www.google-analytics.com https://analytics.google.com",
    "frame-src 'none'",
    "object-src 'none'",
    "base-uri 'self'",
    "form-action 'self'"
  ].join('; ');
  
  response.headers.set('Content-Security-Policy', csp);

  // Cache control based on path
  if (pathname.startsWith('/_next/static/')) {
    // Static assets - cache for 1 year
    response.headers.set('Cache-Control', 'public, max-age=31536000, immutable');
  } else if (pathname.startsWith('/images/')) {
    // Images - cache for 1 month
    response.headers.set('Cache-Control', 'public, max-age=2592000');
  } else if (pathname.startsWith('/api/')) {
    // API routes - no cache by default
    response.headers.set('Cache-Control', 'no-cache, no-store, must-revalidate');
  } else if (pathname === '/sitemap.xml' || pathname === '/robots.txt') {
    // SEO files - cache for 1 day
    response.headers.set('Cache-Control', 'public, max-age=86400');
  } else if (pathname === '/manifest.json') {
    // PWA manifest - cache for 1 week
    response.headers.set('Cache-Control', 'public, max-age=604800');
  } else {
    // HTML pages - cache for 1 hour with revalidation
    response.headers.set('Cache-Control', 'public, max-age=3600, stale-while-revalidate=86400');
  }

  // HSTS for HTTPS
  if (request.nextUrl.protocol === 'https:') {
    response.headers.set('Strict-Transport-Security', 'max-age=31536000; includeSubDomains; preload');
  }

  // Compression hints
  response.headers.set('Vary', 'Accept-Encoding');

  // Performance hints
  if (pathname === '/') {
    // Preload critical resources for homepage
    response.headers.set('Link', [
      '</fonts/inter-var.woff2>; rel=preload; as=font; type=font/woff2; crossorigin=anonymous',
      '</images/hero-bg.webp>; rel=preload; as=image',
      '</styles/critical.css>; rel=preload; as=style'
    ].join(', '));
  }

  // Early hints for critical resources
  if (pathname.startsWith('/jogos') || pathname.startsWith('/cassino')) {
    response.headers.set('Link', [
      '</images/games-bg.webp>; rel=preload; as=image',
      '</api/games>; rel=prefetch'
    ].join(', '));
  }

  // Canonical URL enforcement
  const host = request.headers.get('host');
  const protocol = request.nextUrl.protocol;
  
  // Redirect www to non-www
  if (host?.startsWith('www.')) {
    const newHost = host.replace('www.', '');
    return NextResponse.redirect(`${protocol}//${newHost}${pathname}${request.nextUrl.search}`, 301);
  }

  // Redirect HTTP to HTTPS in production
  if (protocol === 'http:' && process.env.NODE_ENV === 'production') {
    return NextResponse.redirect(`https://${host}${pathname}${request.nextUrl.search}`, 301);
  }

  // Trailing slash handling
  if (pathname !== '/' && pathname.endsWith('/')) {
    return NextResponse.redirect(`${protocol}//${host}${pathname.slice(0, -1)}${request.nextUrl.search}`, 301);
  }

  // Bot detection and rate limiting
  const userAgent = request.headers.get('user-agent') || '';
  const isBot = /bot|crawler|spider|scraper/i.test(userAgent);
  
  if (isBot) {
    // Add special headers for bots
    response.headers.set('X-Robots-Tag', 'index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1');
    
    // Block malicious bots
    const maliciousBots = [
      'AhrefsBot',
      'MJ12bot', 
      'SemrushBot',
      'DotBot',
      'BLEXBot'
    ];
    
    if (maliciousBots.some(bot => userAgent.includes(bot))) {
      return new NextResponse('Forbidden', { status: 403 });
    }
  }

  // Geo-targeting for Brazil
  const requestWithGeo = request as NextRequest & { geo?: { country?: string } };
  const country = requestWithGeo.geo?.country;
  if (country && country !== 'BR') {
    response.headers.set('X-Geo-Country', country);
    // Could add geo-specific content or redirects here
  }

  // A/B testing headers
  const abTestVariant = request.cookies.get('ab-test-variant')?.value || 'A';
  response.headers.set('X-AB-Test-Variant', abTestVariant);

  // Performance monitoring
  response.headers.set('X-Response-Time', Date.now().toString());

  return response;
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!_next/static|_next/image|favicon.ico).*)',
  ],
};
