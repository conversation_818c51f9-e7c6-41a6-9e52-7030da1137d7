'use client';

import { useEffect, useCallback, useRef } from 'react';
import { usePathname } from 'next/navigation';

interface PerformanceMetric {
  name: string;
  value: number;
  rating: 'good' | 'needs-improvement' | 'poor';
  timestamp: number;
}

interface PerformanceOptimizerProps {
  enableResourceHints?: boolean;
  enableImageOptimization?: boolean;
  enableFontOptimization?: boolean;
  enableCriticalCSS?: boolean;
  debug?: boolean;
}

export default function PerformanceOptimizer({
  enableResourceHints = true,
  enableImageOptimization = true,
  enableFontOptimization = true,
  enableCriticalCSS = true,
  debug = false
}: PerformanceOptimizerProps) {
  const pathname = usePathname();
  const metricsRef = useRef<PerformanceMetric[]>([]);
  const observersRef = useRef<{
    intersection?: IntersectionObserver;
    performance?: PerformanceObserver;
  }>({});

  // Core Web Vitals monitoring
  const initWebVitalsMonitoring = useCallback(() => {
    if (typeof window === 'undefined') return;

    // Dynamic import of web-vitals
    import('web-vitals').then(({ onCLS, onINP, onFCP, onLCP, onTTFB }) => {
      const handleMetric = (metric: { name: string; id: string; value: number }) => {
        const rating = getRating(metric.name, metric.value);
        const performanceMetric: PerformanceMetric = {
          name: metric.name,
          value: metric.value,
          rating,
          timestamp: Date.now()
        };

        metricsRef.current.push(performanceMetric);

        if (debug) {
          console.log(`${metric.name}:`, metric.value, `(${rating})`);
        }

        // Send to analytics
        sendToAnalytics(performanceMetric);
      };

      onCLS(handleMetric);
      onINP(handleMetric);
      onFCP(handleMetric);
      onLCP(handleMetric);
      onTTFB(handleMetric);
    });
  }, [debug]);

  // Image optimization with lazy loading
  const initImageOptimization = useCallback(() => {
    if (!enableImageOptimization || typeof window === 'undefined') return;

    const imageObserver = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const img = entry.target as HTMLImageElement;
            
            // Load high-quality image
            if (img.dataset.src) {
              img.src = img.dataset.src;
              img.classList.remove('lazy');
              img.classList.add('loaded');
            }

            // Load srcset if available
            if (img.dataset.srcset) {
              img.srcset = img.dataset.srcset;
            }

            imageObserver.unobserve(img);
          }
        });
      },
      {
        rootMargin: '50px 0px',
        threshold: 0.01
      }
    );

    // Observe all lazy images
    const lazyImages = document.querySelectorAll('img[data-src]');
    lazyImages.forEach((img) => imageObserver.observe(img));

    observersRef.current.intersection = imageObserver;
  }, [enableImageOptimization]);

  // Font optimization
  const initFontOptimization = useCallback(() => {
    if (!enableFontOptimization || typeof window === 'undefined') return;

    // Font loading optimization
    if ('fonts' in document) {
      document.fonts.ready.then(() => {
        document.body.classList.add('fonts-loaded');
        
        // Preload critical fonts
        const criticalFonts = [
          '/fonts/inter-var.woff2',
          '/fonts/inter-bold.woff2'
        ];

        criticalFonts.forEach((fontUrl) => {
          const link = document.createElement('link');
          link.rel = 'preload';
          link.as = 'font';
          link.type = 'font/woff2';
          link.crossOrigin = 'anonymous';
          link.href = fontUrl;
          document.head.appendChild(link);
        });
      });
    }
  }, [enableFontOptimization]);

  // Resource hints optimization
  const initResourceHints = useCallback(() => {
    if (!enableResourceHints || typeof window === 'undefined') return;

    // Prefetch likely next pages based on current page
    const prefetchMap: Record<string, string[]> = {
      '/': ['/jogos', '/cassino', '/sobre'],
      '/jogos': ['/cassino', '/guias', '/faq'],
      '/cassino': ['/jogos', '/promocoes', '/sobre'],
      '/guias': ['/faq', '/glossario', '/jogos'],
      '/faq': ['/suporte', '/sobre', '/guias'],
      '/glossario': ['/guias', '/faq', '/jogos'],
      '/cidades': ['/sobre', '/faq', '/jogos']
    };

    const urlsToPrefetch = prefetchMap[pathname] || [];
    
    urlsToPrefetch.forEach((url) => {
      const link = document.createElement('link');
      link.rel = 'prefetch';
      link.href = url;
      document.head.appendChild(link);
    });

    // Preconnect to critical third-party domains
    const criticalDomains = [
      'https://www.google-analytics.com',
      'https://www.googletagmanager.com',
      'https://fonts.googleapis.com',
      'https://fonts.gstatic.com'
    ];

    criticalDomains.forEach((domain) => {
      const link = document.createElement('link');
      link.rel = 'preconnect';
      link.href = domain;
      if (domain.includes('fonts.gstatic.com')) {
        link.crossOrigin = 'anonymous';
      }
      document.head.appendChild(link);
    });
  }, [enableResourceHints, pathname]);

  // Critical CSS optimization
  const initCriticalCSS = useCallback(() => {
    if (!enableCriticalCSS || typeof window === 'undefined') return;

    // Load non-critical CSS asynchronously
    const nonCriticalCSS = [
      '/styles/components.css',
      '/styles/animations.css'
    ];

    nonCriticalCSS.forEach((cssUrl) => {
      const link = document.createElement('link');
      link.rel = 'preload';
      link.as = 'style';
      link.href = cssUrl;
      link.onload = () => {
        link.rel = 'stylesheet';
      };
      document.head.appendChild(link);
    });
  }, [enableCriticalCSS]);

  // Performance monitoring
  const initPerformanceMonitoring = useCallback(() => {
    if (typeof window === 'undefined') return;

    const performanceObserver = new PerformanceObserver((list) => {
      list.getEntries().forEach((entry) => {
        if (debug) {
          console.log('Performance entry:', entry);
        }

        // Monitor long tasks
        if (entry.entryType === 'longtask') {
          console.warn('Long task detected:', entry.duration);
        }

        // Monitor layout shifts
        if (entry.entryType === 'layout-shift') {
          const layoutShiftEntry = entry as PerformanceEntry & {
            hadRecentInput?: boolean;
            value?: number;
          };
          if (!layoutShiftEntry.hadRecentInput) {
            console.log('Layout shift:', layoutShiftEntry.value);
          }
        }

        // Monitor resource timing
        if (entry.entryType === 'resource') {
          const resourceEntry = entry as PerformanceResourceTiming;
          if (resourceEntry.duration > 1000) {
            console.warn('Slow resource:', resourceEntry.name, resourceEntry.duration);
          }
        }
      });
    });

    // Observe different types of performance entries
    const entryTypes = ['longtask', 'layout-shift', 'resource', 'navigation'];
    entryTypes.forEach((type) => {
      try {
        performanceObserver.observe({ entryTypes: [type] });
      } catch (error) {
        // Some entry types might not be supported
        if (debug) {
          console.log(`Performance observer type ${type} not supported`, error);
        }
      }
    });

    observersRef.current.performance = performanceObserver;
  }, [debug]);

  // Initialize all optimizations
  useEffect(() => {
    initWebVitalsMonitoring();
    initImageOptimization();
    initFontOptimization();
    initResourceHints();
    initCriticalCSS();
    initPerformanceMonitoring();

    // Cleanup function
    return () => {
      const observers = observersRef.current;
      if (observers.intersection) {
        observers.intersection.disconnect();
      }
      if (observers.performance) {
        observers.performance.disconnect();
      }
    };
  }, [
    initWebVitalsMonitoring,
    initImageOptimization,
    initFontOptimization,
    initResourceHints,
    initCriticalCSS,
    initPerformanceMonitoring
  ]);

  return null; // This component doesn't render anything
}

// Helper functions
function getRating(metricName: string, value: number): 'good' | 'needs-improvement' | 'poor' {
  const thresholds: Record<string, [number, number]> = {
    'CLS': [0.1, 0.25],
    'INP': [200, 500],
    'FCP': [1800, 3000],
    'LCP': [2500, 4000],
    'TTFB': [800, 1800]
  };

  const [good, poor] = thresholds[metricName] || [0, 0];
  
  if (value <= good) return 'good';
  if (value <= poor) return 'needs-improvement';
  return 'poor';
}

function sendToAnalytics(metric: PerformanceMetric) {
  // Send to Google Analytics
  if (typeof window !== 'undefined' && 'gtag' in window) {
    const windowWithGtag = window as unknown as { gtag: (...args: unknown[]) => void };
    windowWithGtag.gtag('event', metric.name, {
      event_category: 'Web Vitals',
      event_label: metric.rating,
      value: Math.round(metric.name === 'CLS' ? metric.value * 1000 : metric.value),
      non_interaction: true,
    });
  }

  // Send to custom analytics endpoint
  fetch('/api/analytics/web-vitals', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(metric),
  }).catch(() => {
    // Silently fail if analytics endpoint is not available
  });
}

// Hook for accessing performance metrics
export function usePerformanceMetrics() {
  const metricsRef = useRef<PerformanceMetric[]>([]);

  const getMetrics = useCallback(() => {
    return metricsRef.current;
  }, []);

  const getLatestMetric = useCallback((metricName: string) => {
    return metricsRef.current
      .filter(m => m.name === metricName)
      .sort((a, b) => b.timestamp - a.timestamp)[0];
  }, []);

  const getAverageMetric = useCallback((metricName: string) => {
    const metrics = metricsRef.current.filter(m => m.name === metricName);
    if (metrics.length === 0) return null;
    
    const sum = metrics.reduce((acc, m) => acc + m.value, 0);
    return sum / metrics.length;
  }, []);

  return {
    getMetrics,
    getLatestMetric,
    getAverageMetric
  };
}
