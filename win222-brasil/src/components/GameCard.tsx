'use client';

import { useState } from 'react';
import Link from 'next/link';
import { ThumbnailImage } from './OptimizedImage';
import { trackConversion, generateAffiliateUrl, getPrimaryAffiliate } from '@/lib/affiliate';

interface GameCardProps {
  id: string;
  title: string;
  image: string;
  category: string;
  rating?: number;
  isNew?: boolean;
  isHot?: boolean;
  provider?: string;
  description?: string;
  href?: string;
  onPlay?: () => void;
  onFavorite?: () => void;
  isFavorite?: boolean;
  affiliateCategory?: 'casino' | 'sports' | 'poker' | 'slots' | 'live';
  enableAffiliate?: boolean;
}

export default function GameCard({
  id,
  title,
  image,
  category,
  rating = 0,
  isNew = false,
  isHot = false,
  provider,
  description,
  href,
  onPlay,
  onFavorite,
  isFavorite = false,
  affiliateCategory,
  enableAffiliate = true,
}: GameCardProps) {
  const [isHovered, setIsHovered] = useState(false);
  const [imageError, setImageError] = useState(false);

  // Get affiliate link based on category
  const getAffiliateCategory = () => {
    if (affiliateCategory) return affiliateCategory;

    // Map game categories to affiliate categories
    const categoryMap: Record<string, 'casino' | 'sports' | 'poker' | 'slots' | 'live'> = {
      'Slots': 'slots',
      'Mesa': 'casino',
      'Crash': 'casino',
      'Original': 'casino',
      'Ao Vivo': 'live',
      'Esportes': 'sports',
      'Poker': 'poker'
    };

    return categoryMap[category] || 'casino';
  };

  const handlePlay = (e: React.MouseEvent) => {
    e.preventDefault();

    if (enableAffiliate) {
      // Track conversion and redirect to affiliate link
      const affCategory = getAffiliateCategory();
      const affiliate = getPrimaryAffiliate(affCategory);

      if (affiliate) {
        trackConversion({
          type: 'play',
          affiliateId: affiliate.id,
          metadata: {
            game_id: id,
            game_title: title,
            game_category: category,
            provider: provider
          }
        });

        const affiliateUrl = generateAffiliateUrl(affiliate.id, {
          game: id,
          category: category,
          source: 'game_card'
        });

        window.open(affiliateUrl, '_blank', 'noopener,noreferrer');
        return;
      }
    }

    onPlay?.();
  };

  const handleFavorite = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    onFavorite?.();
  };

  const CardContent = () => (
    <div
      className={`group relative bg-gray-800 rounded-xl overflow-hidden shadow-lg transition-all duration-300 hover:shadow-2xl hover:scale-105 ${
        isHovered ? 'ring-2 ring-yellow-500' : ''
      }`}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Imagem do jogo */}
      <div className="relative aspect-[4/3] overflow-hidden">
        <ThumbnailImage
          src={imageError ? '/images/game-placeholder.jpg' : image}
          alt={title}
          className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-110"
          onError={() => setImageError(true)}
          sizes="(max-width: 640px) 50vw, (max-width: 1024px) 33vw, 25vw"
        />

        {/* Overlay com gradiente */}
        <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

        {/* Badges */}
        <div className="absolute top-2 left-2 flex flex-wrap gap-1">
          {isNew && (
            <span className="bg-green-500 text-white text-xs px-2 py-1 rounded-full font-semibold">
              NOVO
            </span>
          )}
          {isHot && (
            <span className="bg-red-500 text-white text-xs px-2 py-1 rounded-full font-semibold">
              🔥 HOT
            </span>
          )}
        </div>

        {/* Botão de favorito */}
        <button
          onClick={handleFavorite}
          className={`absolute top-2 right-2 p-2 rounded-full transition-all duration-200 ${
            isFavorite
              ? 'bg-red-500 text-white'
              : 'bg-black/50 text-white hover:bg-red-500'
          }`}
          aria-label={isFavorite ? 'Remover dos favoritos' : 'Adicionar aos favoritos'}
        >
          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
            <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z" />
          </svg>
        </button>

        {/* Botão de play (aparece no hover) */}
        <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
          <button
            onClick={handlePlay}
            className="bg-yellow-500 hover:bg-yellow-600 text-black p-4 rounded-full shadow-lg transform scale-90 group-hover:scale-100 transition-transform duration-200"
            aria-label={`Jogar ${title}`}
          >
            <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
              <path d="M8 5v14l11-7z" />
            </svg>
          </button>
        </div>
      </div>

      {/* Informações do jogo */}
      <div className="p-4">
        <div className="flex items-start justify-between mb-2">
          <h3 className="text-white font-semibold text-sm line-clamp-2 group-hover:text-yellow-500 transition-colors">
            {title}
          </h3>
          {rating > 0 && (
            <div className="flex items-center ml-2 flex-shrink-0">
              <svg className="w-4 h-4 text-yellow-500" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" />
              </svg>
              <span className="text-gray-300 text-xs ml-1">{rating.toFixed(1)}</span>
            </div>
          )}
        </div>

        <div className="flex items-center justify-between text-xs text-gray-400">
          <span className="bg-gray-700 px-2 py-1 rounded">{category}</span>
          {provider && (
            <span className="truncate ml-2">{provider}</span>
          )}
        </div>

        {description && (
          <p className="text-gray-400 text-xs mt-2 line-clamp-2">
            {description}
          </p>
        )}

        {/* Botões de ação (mobile) */}
        <div className="flex gap-2 mt-3 md:hidden">
          <button
            onClick={handlePlay}
            className="flex-1 bg-yellow-500 hover:bg-yellow-600 text-black py-2 px-3 rounded-lg text-sm font-semibold transition-colors"
          >
            Jogar
          </button>
          <button
            onClick={handleFavorite}
            className={`p-2 rounded-lg transition-colors ${
              isFavorite
                ? 'bg-red-500 text-white'
                : 'bg-gray-700 text-gray-300 hover:bg-red-500 hover:text-white'
            }`}
          >
            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z" />
            </svg>
          </button>
        </div>
      </div>
    </div>
  );

  // Se há href, envolver em Link
  if (href) {
    return (
      <Link href={href} className="block">
        <CardContent />
      </Link>
    );
  }

  return <CardContent />;
}

// Componente para grid de jogos responsivo
export function GameGrid({ 
  children, 
  className = '' 
}: { 
  children: React.ReactNode; 
  className?: string; 
}) {
  return (
    <div className={`grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 2xl:grid-cols-6 gap-4 ${className}`}>
      {children}
    </div>
  );
}

// Componente para skeleton loading
export function GameCardSkeleton() {
  return (
    <div className="bg-gray-800 rounded-xl overflow-hidden animate-pulse">
      <div className="aspect-[4/3] bg-gray-700" />
      <div className="p-4">
        <div className="h-4 bg-gray-700 rounded mb-2" />
        <div className="h-3 bg-gray-700 rounded w-2/3" />
      </div>
    </div>
  );
}

// Hook para gerenciar favoritos
export function useFavorites() {
  const [favorites, setFavorites] = useState<string[]>([]);

  const toggleFavorite = (gameId: string) => {
    setFavorites(prev => 
      prev.includes(gameId)
        ? prev.filter(id => id !== gameId)
        : [...prev, gameId]
    );
  };

  const isFavorite = (gameId: string) => favorites.includes(gameId);

  return { favorites, toggleFavorite, isFavorite };
}
