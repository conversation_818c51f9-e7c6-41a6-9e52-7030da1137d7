'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';

interface RelatedItem {
  title: string;
  href: string;
  description: string;
  category: string;
  keywords: string[];
}

// 相关内容数据库
const relatedContentDatabase: RelatedItem[] = [
  // 首页相关
  {
    title: "Explore Nossos Jogos Premium",
    href: "/jogos",
    description: "Descubra mais de 1000 jogos online, incluindo slots, poker, blackjack e muito mais",
    category: "games",
    keywords: ["win222", "jogos online", "slots", "poker"]
  },
  {
    title: "Cassino Online Completo",
    href: "/cassino",
    description: "Experimente o melhor cassino online do Brasil com dealers ao vivo e jackpots progressivos",
    category: "casino",
    keywords: ["win222", "cassino online", "dealers ao vivo", "jackpots"]
  },
  {
    title: "Conheça Nossa História",
    href: "/sobre",
    description: "Saiba mais sobre o Win222 Brasil, nossa missão e compromisso com o jogo responsável",
    category: "about",
    keywords: ["win222", "empresa brasileira", "jogo responsável"]
  },

  // Jogos relacionados
  {
    title: "Estratégias de Slots Avançadas",
    href: "/jogos#estrategias-slots",
    description: "Aprenda técnicas profissionais para maximizar seus ganhos em slots online",
    category: "strategy",
    keywords: ["slots", "estratégias", "RTP", "volatilidade"]
  },
  {
    title: "Guia Completo de Blackjack",
    href: "/jogos#blackjack-estrategia",
    description: "Domine a estratégia básica do blackjack e reduza a vantagem da casa",
    category: "strategy",
    keywords: ["blackjack", "estratégia básica", "cartas"]
  },
  {
    title: "Cassino Ao Vivo Premium",
    href: "/cassino#ao-vivo",
    description: "Jogue com dealers brasileiros em tempo real no melhor cassino ao vivo",
    category: "live",
    keywords: ["cassino ao vivo", "dealers brasileiros", "tempo real"]
  },

  // Cassino relacionados
  {
    title: "Todos os Jogos Disponíveis",
    href: "/jogos",
    description: "Navegue por nossa coleção completa de mais de 1000 jogos online",
    category: "games",
    keywords: ["jogos online", "coleção completa", "win222"]
  },
  {
    title: "Provedores de Jogos Premium",
    href: "/cassino#provedores",
    description: "Conheça os melhores provedores mundiais: Pragmatic Play, Evolution Gaming e mais",
    category: "providers",
    keywords: ["provedores", "Pragmatic Play", "Evolution Gaming"]
  },
  {
    title: "Jogo Responsável",
    href: "/sobre#jogo-responsavel",
    description: "Ferramentas e recursos para manter o jogo sempre divertido e seguro",
    category: "responsible",
    keywords: ["jogo responsável", "autocontrole", "limites"]
  },

  // Sobre relacionados
  {
    title: "Nossos Jogos Exclusivos",
    href: "/jogos",
    description: "Descubra jogos exclusivos e as últimas novidades do Win222 Brasil",
    category: "games",
    keywords: ["jogos exclusivos", "novidades", "win222"]
  },
  {
    title: "Tecnologia de Cassino",
    href: "/cassino#tecnologia",
    description: "Conheça a tecnologia de ponta que oferece a melhor experiência de jogo",
    category: "technology",
    keywords: ["tecnologia", "segurança", "performance"]
  },
  {
    title: "Suporte Especializado",
    href: "/sobre#suporte",
    description: "Nossa equipe de suporte 24/7 está sempre pronta para ajudar",
    category: "support",
    keywords: ["suporte 24/7", "atendimento", "ajuda"]
  }
];

interface RelatedContentProps {
  currentPage?: string;
  category?: string;
  maxItems?: number;
  keywords?: string[];
}

export default function RelatedContent({ 
  currentPage, 
  category, 
  maxItems = 3,
  keywords = []
}: RelatedContentProps) {
  const pathname = usePathname();
  const currentPath = currentPage || pathname;

  // Filtrar conteúdo relacionado baseado na página atual e categoria
  const getRelatedContent = (): RelatedItem[] => {
    let filtered = relatedContentDatabase.filter(item => item.href !== currentPath);

    // Se categoria especificada, priorizar itens da mesma categoria
    if (category) {
      const categoryItems = filtered.filter(item => item.category === category);
      const otherItems = filtered.filter(item => item.category !== category);
      filtered = [...categoryItems, ...otherItems];
    }

    // Se keywords fornecidas, priorizar por relevância
    if (keywords.length > 0) {
      filtered.sort((a, b) => {
        const aScore = keywords.reduce((score, keyword) => {
          return score + (a.keywords.some(k => k.includes(keyword.toLowerCase())) ? 1 : 0);
        }, 0);
        const bScore = keywords.reduce((score, keyword) => {
          return score + (b.keywords.some(k => k.includes(keyword.toLowerCase())) ? 1 : 0);
        }, 0);
        return bScore - aScore;
      });
    }

    return filtered.slice(0, maxItems);
  };

  const relatedItems = getRelatedContent();

  if (relatedItems.length === 0) {
    return null;
  }

  return (
    <section className="py-16 bg-gray-800/30">
      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          <h2 className="text-3xl font-bold text-white text-center mb-12">
            Conteúdo <span className="text-yellow-400">Relacionado</span>
          </h2>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {relatedItems.map((item, index) => (
              <Link
                key={item.href}
                href={item.href}
                className="group bg-gray-800/50 rounded-2xl p-6 hover:bg-gray-700/50 transition-all duration-300 border border-gray-700/50 hover:border-yellow-400/30"
              >
                <div className="flex items-start justify-between mb-4">
                  <span className="text-xs font-semibold text-yellow-400 uppercase tracking-wider">
                    {item.category === 'games' && '🎮 Jogos'}
                    {item.category === 'casino' && '🎰 Cassino'}
                    {item.category === 'strategy' && '📚 Estratégia'}
                    {item.category === 'about' && 'ℹ️ Sobre'}
                    {item.category === 'live' && '📺 Ao Vivo'}
                    {item.category === 'providers' && '🏢 Provedores'}
                    {item.category === 'responsible' && '🛡️ Responsável'}
                    {item.category === 'technology' && '⚡ Tecnologia'}
                    {item.category === 'support' && '🤝 Suporte'}
                  </span>
                  <svg 
                    className="w-5 h-5 text-gray-400 group-hover:text-yellow-400 transition-colors" 
                    fill="none" 
                    stroke="currentColor" 
                    viewBox="0 0 24 24"
                  >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </div>
                
                <h3 className="text-xl font-bold text-white mb-3 group-hover:text-yellow-400 transition-colors">
                  {item.title}
                </h3>
                
                <p className="text-gray-300 text-sm leading-relaxed">
                  {item.description}
                </p>
                
                <div className="mt-4 flex flex-wrap gap-2">
                  {item.keywords.slice(0, 3).map((keyword) => (
                    <span 
                      key={keyword}
                      className="text-xs bg-gray-700/50 text-gray-300 px-2 py-1 rounded-full"
                    >
                      {keyword}
                    </span>
                  ))}
                </div>
              </Link>
            ))}
          </div>
          
          <div className="text-center mt-12">
            <p className="text-gray-400 text-sm mb-4">
              Explore mais conteúdo do <strong className="text-yellow-400">Win222 Brasil</strong>
            </p>
            <div className="flex flex-wrap justify-center gap-4">
              <Link 
                href="/jogos" 
                className="text-yellow-400 hover:text-yellow-300 text-sm font-medium transition-colors"
              >
                Todos os Jogos →
              </Link>
              <Link 
                href="/cassino" 
                className="text-yellow-400 hover:text-yellow-300 text-sm font-medium transition-colors"
              >
                Cassino Completo →
              </Link>
              <Link 
                href="/sobre" 
                className="text-yellow-400 hover:text-yellow-300 text-sm font-medium transition-colors"
              >
                Sobre Nós →
              </Link>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}

// Hook para obter conteúdo relacionado baseado em contexto
export function useRelatedContent(context: {
  currentPage?: string;
  category?: string;
  keywords?: string[];
}) {
  return {
    getRelatedItems: (maxItems = 3) => {
      let filtered = relatedContentDatabase.filter(item => item.href !== context.currentPage);
      
      if (context.category) {
        const categoryItems = filtered.filter(item => item.category === context.category);
        const otherItems = filtered.filter(item => item.category !== context.category);
        filtered = [...categoryItems, ...otherItems];
      }
      
      if (context.keywords && context.keywords.length > 0) {
        filtered.sort((a, b) => {
          const aScore = context.keywords!.reduce((score, keyword) => {
            return score + (a.keywords.some(k => k.includes(keyword.toLowerCase())) ? 1 : 0);
          }, 0);
          const bScore = context.keywords!.reduce((score, keyword) => {
            return score + (b.keywords.some(k => k.includes(keyword.toLowerCase())) ? 1 : 0);
          }, 0);
          return bScore - aScore;
        });
      }
      
      return filtered.slice(0, maxItems);
    }
  };
}
