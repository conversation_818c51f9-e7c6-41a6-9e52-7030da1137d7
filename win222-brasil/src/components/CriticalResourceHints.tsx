// Server component for critical resource hints
export function CriticalResourceHints() {
  return (
    <>
      {/* Critical CSS */}
      <link rel="preload" href="/styles/critical.css" as="style" />
      
      {/* Critical JavaScript */}
      <link rel="preload" href="/_next/static/chunks/main.js" as="script" />
      
      {/* Critical fonts */}
      <link 
        rel="preload" 
        href="/fonts/inter-var.woff2" 
        as="font" 
        type="font/woff2" 
        crossOrigin="anonymous" 
      />
      
      {/* Critical images */}
      <link rel="preload" href="/images/hero-bg.webp" as="image" />
      <link rel="preload" href="/images/logo.webp" as="image" />
      
      {/* DNS prefetch for external resources */}
      <link rel="dns-prefetch" href="//www.google-analytics.com" />
      <link rel="dns-prefetch" href="//www.googletagmanager.com" />
      <link rel="dns-prefetch" href="//fonts.googleapis.com" />
      <link rel="dns-prefetch" href="//fonts.gstatic.com" />
      <link rel="dns-prefetch" href="//connect.facebook.net" />

      {/* Preconnect to critical third-party origins */}
      <link rel="preconnect" href="https://fonts.googleapis.com" />
      <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
      <link rel="preconnect" href="https://www.google-analytics.com" />
    </>
  );
}
