'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';

interface BreadcrumbItem {
  label: string;
  href: string;
  isActive?: boolean;
}

interface BreadcrumbProps {
  items?: BreadcrumbItem[];
  showHome?: boolean;
  className?: string;
}

// Mapeamento de rotas para breadcrumbs
const routeMapping: Record<string, BreadcrumbItem[]> = {
  '/': [
    { label: 'Início', href: '/', isActive: true }
  ],
  '/jogos': [
    { label: 'Início', href: '/' },
    { label: 'Jogos Online', href: '/jogos', isActive: true }
  ],
  '/cassino': [
    { label: 'Início', href: '/' },
    { label: 'Cassino Online', href: '/cassino', isActive: true }
  ],
  '/sobre': [
    { label: 'Início', href: '/' },
    { label: 'Sobre Nós', href: '/sobre', isActive: true }
  ],
  '/apostas': [
    { label: 'Início', href: '/' },
    { label: 'Apostas Esportivas', href: '/apostas', isActive: true }
  ],
  '/jogo-responsavel': [
    { label: 'Início', href: '/' },
    { label: 'Sobre Nós', href: '/sobre' },
    { label: 'Jogo Responsável', href: '/jogo-responsavel', isActive: true }
  ],
  '/suporte': [
    { label: 'Início', href: '/' },
    { label: 'Suporte', href: '/suporte', isActive: true }
  ],
  '/termos': [
    { label: 'Início', href: '/' },
    { label: 'Termos de Uso', href: '/termos', isActive: true }
  ],
  '/privacidade': [
    { label: 'Início', href: '/' },
    { label: 'Política de Privacidade', href: '/privacidade', isActive: true }
  ]
};

export default function Breadcrumb({ 
  items, 
  showHome = true, 
  className = '' 
}: BreadcrumbProps) {
  const pathname = usePathname();
  
  // Se items não fornecidos, usar mapeamento automático
  const breadcrumbItems = items || routeMapping[pathname] || [
    { label: 'Início', href: '/' },
    { label: 'Página', href: pathname, isActive: true }
  ];

  // Se showHome é false, remover o primeiro item (Início)
  const finalItems = showHome ? breadcrumbItems : breadcrumbItems.slice(1);

  if (finalItems.length <= 1) {
    return null; // Não mostrar breadcrumb se só tem um item
  }

  return (
    <nav 
      className={`py-4 ${className}`}
      aria-label="Breadcrumb"
    >
      <div className="container mx-auto px-4">
        <ol className="flex items-center space-x-2 text-sm">
          {finalItems.map((item, index) => (
            <li key={item.href} className="flex items-center">
              {index > 0 && (
                <svg 
                  className="w-4 h-4 text-gray-400 mx-2" 
                  fill="none" 
                  stroke="currentColor" 
                  viewBox="0 0 24 24"
                  aria-hidden="true"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              )}
              
              {item.isActive ? (
                <span 
                  className="text-yellow-400 font-medium"
                  aria-current="page"
                >
                  {item.label}
                </span>
              ) : (
                <Link 
                  href={item.href}
                  className="text-gray-300 hover:text-white transition-colors duration-200"
                >
                  {item.label}
                </Link>
              )}
            </li>
          ))}
        </ol>
      </div>
    </nav>
  );
}

// Componente de breadcrumb estruturado para SEO
export function StructuredBreadcrumb({ 
  items, 
  showHome = true 
}: BreadcrumbProps) {
  const pathname = usePathname();
  
  const breadcrumbItems = items || routeMapping[pathname] || [
    { label: 'Início', href: '/' },
    { label: 'Página', href: pathname, isActive: true }
  ];

  const finalItems = showHome ? breadcrumbItems : breadcrumbItems.slice(1);

  if (finalItems.length <= 1) {
    return null;
  }

  // Gerar dados estruturados para SEO
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": finalItems.map((item, index) => ({
      "@type": "ListItem",
      "position": index + 1,
      "name": item.label,
      "item": `https://win222brasil.com${item.href}`
    }))
  };

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
      />
      <Breadcrumb items={finalItems} showHome={false} />
    </>
  );
}

// Hook para obter breadcrumb items programaticamente
export function useBreadcrumb(customItems?: BreadcrumbItem[]) {
  const pathname = usePathname();
  
  return {
    items: customItems || routeMapping[pathname] || [
      { label: 'Início', href: '/' },
      { label: 'Página', href: pathname, isActive: true }
    ],
    pathname
  };
}

// Componente de navegação contextual
export function ContextualNavigation() {
  const pathname = usePathname();
  
  const getContextualLinks = () => {
    switch (pathname) {
      case '/':
        return [
          { label: 'Explore Nossos Jogos', href: '/jogos', description: 'Mais de 1000 jogos online' },
          { label: 'Cassino Premium', href: '/cassino', description: 'Dealers ao vivo e jackpots' },
          { label: 'Conheça Nossa História', href: '/sobre', description: 'Empresa 100% brasileira' }
        ];
      case '/jogos':
        return [
          { label: 'Cassino Completo', href: '/cassino', description: 'Experiência premium de cassino' },
          { label: 'Sobre o Win222', href: '/sobre', description: 'Nossa missão e valores' },
          { label: 'Jogo Responsável', href: '/sobre#jogo-responsavel', description: 'Ferramentas de autocontrole' }
        ];
      case '/cassino':
        return [
          { label: 'Todos os Jogos', href: '/jogos', description: 'Coleção completa de jogos' },
          { label: 'Estratégias de Jogo', href: '/jogos#estrategias', description: 'Dicas para maximizar ganhos' },
          { label: 'Tecnologia e Segurança', href: '/sobre#tecnologia', description: 'Nossa infraestrutura' }
        ];
      case '/sobre':
        return [
          { label: 'Nossos Jogos', href: '/jogos', description: 'Descubra nossa coleção' },
          { label: 'Cassino Online', href: '/cassino', description: 'Experiência premium' },
          { label: 'Suporte 24/7', href: '/suporte', description: 'Estamos aqui para ajudar' }
        ];
      default:
        return [
          { label: 'Página Inicial', href: '/', description: 'Voltar ao início' },
          { label: 'Jogos Online', href: '/jogos', description: 'Explore nossos jogos' },
          { label: 'Cassino Premium', href: '/cassino', description: 'Experiência completa' }
        ];
    }
  };

  const contextualLinks = getContextualLinks();

  return (
    <div className="bg-gray-800/20 py-8">
      <div className="container mx-auto px-4">
        <h3 className="text-lg font-semibold text-white mb-6 text-center">
          Continue Explorando o <span className="text-yellow-400">Win222 Brasil</span>
        </h3>
        <div className="grid md:grid-cols-3 gap-4">
          {contextualLinks.map((link) => (
            <Link
              key={link.href}
              href={link.href}
              className="group bg-gray-800/30 rounded-lg p-4 hover:bg-gray-700/50 transition-all duration-300 border border-gray-700/30 hover:border-yellow-400/30"
            >
              <h4 className="text-white font-medium mb-2 group-hover:text-yellow-400 transition-colors">
                {link.label}
              </h4>
              <p className="text-gray-400 text-sm">
                {link.description}
              </p>
              <div className="flex items-center mt-3 text-yellow-400 text-sm">
                <span className="group-hover:mr-2 transition-all">Saiba mais</span>
                <svg 
                  className="w-4 h-4 opacity-0 group-hover:opacity-100 transition-all" 
                  fill="none" 
                  stroke="currentColor" 
                  viewBox="0 0 24 24"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </div>
            </Link>
          ))}
        </div>
      </div>
    </div>
  );
}
