'use client';

import { useState, useEffect } from 'react';
import MobileNavigation from './MobileNavigation';
import { HeroImage, ThumbnailImage } from './OptimizedImage';
import GameCard, { GameGrid, useFavorites } from './GameCard';
import AffiliateButton from './AffiliateButton';
import ConversionBanner from './ConversionBanner';

// Dados de exemplo para jogos
const featuredGames = [
  {
    id: '1',
    title: 'Fortune Tiger',
    image: '/images/games/fortune-tiger.jpg',
    category: 'Slots',
    rating: 4.8,
    isHot: true,
    provider: 'PG Soft',
    description: 'Jogo de slots com tema oriental e grandes prêmios',
    affiliateCategory: 'slots' as const,
  },
  {
    id: '2',
    title: 'Aviator',
    image: '/images/games/aviator.jpg',
    category: 'Crash',
    rating: 4.9,
    isNew: true,
    provider: 'Spribe',
    description: 'Jogo de multiplicador emocionante',
    affiliateCategory: 'casino' as const,
  },
  {
    id: '3',
    title: 'Blackjack Premium',
    image: '/images/games/blackjack.jpg',
    category: 'Mesa',
    rating: 4.7,
    provider: 'Evolution',
    description: 'Blackjack clássico com dealers ao vivo',
    affiliateCategory: 'live' as const,
  },
  {
    id: '4',
    title: 'Roleta Brasileira',
    image: '/images/games/roleta.jpg',
    category: 'Mesa',
    rating: 4.6,
    provider: 'Pragmatic',
    description: 'Roleta com regras brasileiras',
    affiliateCategory: 'casino' as const,
  },
  {
    id: '5',
    title: 'Sweet Bonanza',
    image: '/images/games/sweet-bonanza.jpg',
    category: 'Slots',
    rating: 4.8,
    isHot: true,
    provider: 'Pragmatic',
    description: 'Slot doce com multiplicadores',
    affiliateCategory: 'slots' as const,
  },
  {
    id: '6',
    title: 'Mines',
    image: '/images/games/mines.jpg',
    category: 'Original',
    rating: 4.5,
    isNew: true,
    provider: 'Win222',
    description: 'Jogo original de estratégia e sorte',
    affiliateCategory: 'casino' as const,
  },
];

const categories = [
  { name: 'Slots', icon: '🎰', count: 500 },
  { name: 'Mesa', icon: '🃏', count: 50 },
  { name: 'Crash', icon: '🚀', count: 25 },
  { name: 'Original', icon: '⭐', count: 30 },
  { name: 'Ao Vivo', icon: '📹', count: 40 },
  { name: 'Esportes', icon: '⚽', count: 100 },
];

export default function HomePage() {
  const [isLoaded, setIsLoaded] = useState(false);
  const { favorites, toggleFavorite, isFavorite } = useFavorites();

  useEffect(() => {
    setIsLoaded(true);
  }, []);

  const handlePlayGame = (gameId: string) => {
    console.log(`Playing game: ${gameId}`);
    // Aqui você implementaria a lógica para abrir o jogo
  };

  return (
    <div className="min-h-screen bg-gray-900">
      <MobileNavigation />

      {/* Conversion Banners */}
      <ConversionBanner
        type="welcome"
        position="top"
        dismissible={true}
        autoShow={true}
        delay={5000}
      />

      {/* Hero Section */}
      <section className="relative overflow-hidden">
        <div className="win222-gradient min-h-[60vh] flex items-center">
          <div className="container mx-auto px-4 py-12">
            <div className="grid lg:grid-cols-2 gap-8 items-center">
              <div className={`text-center lg:text-left ${isLoaded ? 'animate-fade-in' : 'opacity-0'}`}>
                <h1 className="text-4xl md:text-6xl font-bold text-white mb-6">
                  <span className="win222-gold">Win222</span>
                  <br />
                  Brasil
                </h1>
                <p className="text-xl text-gray-300 mb-8 max-w-lg">
                  A plataforma líder em jogos online no Brasil com mais de 1000 jogos seguros, 
                  bônus exclusivos e suporte 24/7.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
                  <AffiliateButton
                    affiliateId="welcome_bonus"
                    variant="primary"
                    size="lg"
                    context="hero-cta"
                    additionalParams={{ source: 'homepage_hero' }}
                  >
                    Cadastrar Grátis
                  </AffiliateButton>
                  <AffiliateButton
                    affiliateId="main_casino"
                    variant="outline"
                    size="lg"
                    context="hero-games"
                    additionalParams={{ source: 'homepage_hero' }}
                  >
                    Ver Jogos
                  </AffiliateButton>
                </div>
              </div>
              <div className={`hidden lg:block ${isLoaded ? 'animate-slide-up' : 'opacity-0'}`}>
                <HeroImage
                  src="/images/hero-games.jpg"
                  alt="Jogos Win222 Brasil"
                  width={600}
                  height={400}
                  className="rounded-2xl shadow-2xl"
                />
              </div>
            </div>
          </div>
        </div>

        {/* Floating stats */}
        <div className="absolute bottom-0 left-0 right-0 transform translate-y-1/2">
          <div className="container mx-auto px-4">
            <div className="bg-gray-800 rounded-2xl shadow-2xl p-6">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
                <div>
                  <div className="text-2xl font-bold text-yellow-500">1000+</div>
                  <div className="text-gray-400 text-sm">Jogos</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-yellow-500">50K+</div>
                  <div className="text-gray-400 text-sm">Jogadores</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-yellow-500">24/7</div>
                  <div className="text-gray-400 text-sm">Suporte</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-yellow-500">R$ 1M+</div>
                  <div className="text-gray-400 text-sm">Prêmios</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Categories Section */}
      <section className="py-20 mt-16">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-white text-center mb-12">
            Categorias de Jogos
          </h2>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
            {categories.map((category, index) => (
              <div
                key={category.name}
                className={`bg-gray-800 hover:bg-gray-700 p-6 rounded-xl text-center transition-all duration-300 hover-optimized cursor-pointer ${
                  isLoaded ? 'animate-fade-in' : 'opacity-0'
                }`}
                style={{ animationDelay: `${index * 100}ms` }}
              >
                <div className="text-3xl mb-2">{category.icon}</div>
                <h3 className="text-white font-semibold mb-1">{category.name}</h3>
                <p className="text-gray-400 text-sm">{category.count} jogos</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Featured Games Section */}
      <section className="py-20 bg-gray-800/50">
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-between mb-12">
            <h2 className="text-3xl font-bold text-white">
              Jogos em Destaque
            </h2>
            <button className="text-yellow-500 hover:text-yellow-400 font-semibold">
              Ver Todos →
            </button>
          </div>
          
          <GameGrid>
            {featuredGames.map((game, index) => (
              <div
                key={game.id}
                className={`${isLoaded ? 'animate-fade-in' : 'opacity-0'}`}
                style={{ animationDelay: `${index * 150}ms` }}
              >
                <GameCard
                  {...game}
                  onPlay={() => handlePlayGame(game.id)}
                  onFavorite={() => toggleFavorite(game.id)}
                  isFavorite={isFavorite(game.id)}
                />
              </div>
            ))}
          </GameGrid>
        </div>
      </section>

      {/* Promotions Section */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-white text-center mb-12">
            Promoções Exclusivas
          </h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div className="bg-gradient-to-r from-yellow-500 to-orange-500 p-8 rounded-2xl text-black">
              <h3 className="text-2xl font-bold mb-4">Bônus de Boas-vindas</h3>
              <p className="text-lg mb-4">100% até R$ 1.000 + 100 giros grátis</p>
              <AffiliateButton
                affiliateId="welcome_bonus"
                variant="secondary"
                size="md"
                context="promo-welcome"
                additionalParams={{ promo: 'welcome_bonus' }}
                className="bg-black text-white hover:bg-gray-800"
              >
                Resgatar
              </AffiliateButton>
            </div>
            <div className="bg-gradient-to-r from-purple-500 to-pink-500 p-8 rounded-2xl text-white">
              <h3 className="text-2xl font-bold mb-4">Cashback Semanal</h3>
              <p className="text-lg mb-4">Até 15% de volta toda semana</p>
              <AffiliateButton
                affiliateId="main_casino"
                variant="secondary"
                size="md"
                context="promo-cashback"
                additionalParams={{ promo: 'cashback' }}
                className="bg-white text-purple-500 hover:bg-gray-100"
              >
                Participar
              </AffiliateButton>
            </div>
            <div className="bg-gradient-to-r from-green-500 to-blue-500 p-8 rounded-2xl text-white md:col-span-2 lg:col-span-1">
              <h3 className="text-2xl font-bold mb-4">Torneio Mensal</h3>
              <p className="text-lg mb-4">R$ 50.000 em prêmios</p>
              <AffiliateButton
                affiliateId="main_casino"
                variant="secondary"
                size="md"
                context="promo-tournament"
                additionalParams={{ promo: 'tournament' }}
                className="bg-white text-green-500 hover:bg-gray-100"
              >
                Inscrever-se
              </AffiliateButton>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 border-t border-gray-700 py-12">
        <div className="container mx-auto px-4">
          <div className="grid md:grid-cols-4 gap-8">
            <div>
              <h3 className="text-white font-bold text-lg mb-4">Win222 Brasil</h3>
              <p className="text-gray-400 text-sm">
                A plataforma de jogos online mais confiável do Brasil.
              </p>
            </div>
            <div>
              <h4 className="text-white font-semibold mb-4">Jogos</h4>
              <ul className="space-y-2 text-gray-400 text-sm">
                <li><a href="/slots" className="hover:text-white">Slots</a></li>
                <li><a href="/mesa" className="hover:text-white">Jogos de Mesa</a></li>
                <li><a href="/ao-vivo" className="hover:text-white">Cassino Ao Vivo</a></li>
                <li><a href="/esportes" className="hover:text-white">Apostas Esportivas</a></li>
              </ul>
            </div>
            <div>
              <h4 className="text-white font-semibold mb-4">Suporte</h4>
              <ul className="space-y-2 text-gray-400 text-sm">
                <li><a href="/ajuda" className="hover:text-white">Central de Ajuda</a></li>
                <li><a href="/contato" className="hover:text-white">Contato</a></li>
                <li><a href="/chat" className="hover:text-white">Chat 24/7</a></li>
              </ul>
            </div>
            <div>
              <h4 className="text-white font-semibold mb-4">Legal</h4>
              <ul className="space-y-2 text-gray-400 text-sm">
                <li><a href="/termos" className="hover:text-white">Termos de Uso</a></li>
                <li><a href="/privacidade" className="hover:text-white">Privacidade</a></li>
                <li><a href="/responsavel" className="hover:text-white">Jogo Responsável</a></li>
              </ul>
            </div>
          </div>
          <div className="border-t border-gray-700 mt-8 pt-8 text-center text-gray-400 text-sm">
            <p>&copy; 2024 Win222 Brasil. Todos os direitos reservados.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}
