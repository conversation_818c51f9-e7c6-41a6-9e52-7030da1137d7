'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';
import { trackConversion } from '@/lib/affiliate';

interface ABTest {
  id: string;
  name: string;
  variants: ABTestVariant[];
  isActive: boolean;
  trafficAllocation: number; // 0-1, percentage of traffic to include
}

interface ABTestVariant {
  id: string;
  name: string;
  weight: number; // Relative weight for distribution
  config: Record<string, any>;
}

interface ABTestContext {
  getVariant: (testId: string) => ABTestVariant | null;
  trackEvent: (testId: string, event: string, value?: number) => void;
  isInTest: (testId: string) => boolean;
}

const ABTestContext = createContext<ABTestContext | null>(null);

// A/B Test configurations
const AB_TESTS: Record<string, ABTest> = {
  hero_cta_buttons: {
    id: 'hero_cta_buttons',
    name: '<PERSON> CTA Button Variants',
    isActive: true,
    trafficAllocation: 1.0,
    variants: [
      {
        id: 'control',
        name: 'Original Buttons',
        weight: 50,
        config: {
          primaryText: 'Cadastrar Grátis',
          secondaryText: 'Ver Jogos',
          primaryColor: 'yellow',
          layout: 'horizontal'
        }
      },
      {
        id: 'variant_a',
        name: 'Urgency Buttons',
        weight: 25,
        config: {
          primaryText: 'Começar Agora - Grátis!',
          secondaryText: 'Explorar Jogos',
          primaryColor: 'orange',
          layout: 'horizontal'
        }
      },
      {
        id: 'variant_b',
        name: 'Benefit-focused Buttons',
        weight: 25,
        config: {
          primaryText: 'Ganhar R$ 5.000 Grátis',
          secondaryText: 'Ver Todos os Bônus',
          primaryColor: 'green',
          layout: 'vertical'
        }
      }
    ]
  },
  
  banner_timing: {
    id: 'banner_timing',
    name: 'Conversion Banner Timing',
    isActive: true,
    trafficAllocation: 0.8,
    variants: [
      {
        id: 'immediate',
        name: 'Show Immediately',
        weight: 33,
        config: { delay: 0 }
      },
      {
        id: 'delayed_3s',
        name: 'Show After 3 Seconds',
        weight: 33,
        config: { delay: 3000 }
      },
      {
        id: 'delayed_10s',
        name: 'Show After 10 Seconds',
        weight: 34,
        config: { delay: 10000 }
      }
    ]
  },

  game_card_layout: {
    id: 'game_card_layout',
    name: 'Game Card Layout Test',
    isActive: true,
    trafficAllocation: 0.5,
    variants: [
      {
        id: 'standard',
        name: 'Standard Layout',
        weight: 50,
        config: {
          showRating: true,
          showProvider: true,
          buttonStyle: 'overlay'
        }
      },
      {
        id: 'minimal',
        name: 'Minimal Layout',
        weight: 50,
        config: {
          showRating: false,
          showProvider: false,
          buttonStyle: 'bottom'
        }
      }
    ]
  }
};

export function ABTestProvider({ children }: { children: React.ReactNode }) {
  const [userTests, setUserTests] = useState<Record<string, string>>({});
  const [isInitialized, setIsInitialized] = useState(false);

  useEffect(() => {
    // Initialize user's test assignments
    const initializeTests = () => {
      const storedTests = localStorage.getItem('ab_tests');
      let tests: Record<string, string> = {};

      if (storedTests) {
        try {
          tests = JSON.parse(storedTests);
        } catch (error) {
          console.error('Error parsing stored A/B tests:', error);
        }
      }

      // Assign user to tests they're not already in
      Object.values(AB_TESTS).forEach(test => {
        if (!test.isActive) return;
        if (tests[test.id]) return; // Already assigned

        // Check if user should be included in this test
        if (Math.random() > test.trafficAllocation) return;

        // Select variant based on weights
        const totalWeight = test.variants.reduce((sum, variant) => sum + variant.weight, 0);
        let random = Math.random() * totalWeight;
        
        for (const variant of test.variants) {
          random -= variant.weight;
          if (random <= 0) {
            tests[test.id] = variant.id;
            break;
          }
        }
      });

      setUserTests(tests);
      localStorage.setItem('ab_tests', JSON.stringify(tests));
      setIsInitialized(true);

      // Track test assignments
      Object.entries(tests).forEach(([testId, variantId]) => {
        trackConversion({
          type: 'view',
          affiliateId: 'main_casino',
          metadata: {
            ab_test: testId,
            variant: variantId,
            event: 'test_assignment'
          }
        });
      });
    };

    initializeTests();
  }, []);

  const getVariant = (testId: string): ABTestVariant | null => {
    if (!isInitialized) return null;
    
    const test = AB_TESTS[testId];
    const variantId = userTests[testId];
    
    if (!test || !variantId) return null;
    
    return test.variants.find(v => v.id === variantId) || null;
  };

  const trackEvent = (testId: string, event: string, value?: number) => {
    const variant = getVariant(testId);
    if (!variant) return;

    trackConversion({
      type: 'click',
      affiliateId: 'main_casino',
      value,
      metadata: {
        ab_test: testId,
        variant: variant.id,
        event
      }
    });
  };

  const isInTest = (testId: string): boolean => {
    return !!userTests[testId];
  };

  const contextValue: ABTestContext = {
    getVariant,
    trackEvent,
    isInTest
  };

  return (
    <ABTestContext.Provider value={contextValue}>
      {children}
    </ABTestContext.Provider>
  );
}

export function useABTest() {
  const context = useContext(ABTestContext);
  if (!context) {
    throw new Error('useABTest must be used within ABTestProvider');
  }
  return context;
}

// Hook for specific test variants
export function useABTestVariant(testId: string) {
  const { getVariant, trackEvent, isInTest } = useABTest();
  
  const variant = getVariant(testId);
  const config = variant?.config || {};
  
  const track = (event: string, value?: number) => {
    trackEvent(testId, event, value);
  };

  return {
    variant,
    config,
    track,
    isInTest: isInTest(testId)
  };
}
