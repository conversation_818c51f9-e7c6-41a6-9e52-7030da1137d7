import React from 'react';

// Semantic HTML components for better SEO and accessibility

interface SemanticSectionProps {
  children: React.ReactNode;
  className?: string;
  id?: string;
  'aria-label'?: string;
  'aria-labelledby'?: string;
}

interface SemanticArticleProps extends SemanticSectionProps {
  itemScope?: boolean;
  itemType?: string;
}

interface SemanticHeaderProps extends SemanticSectionProps {
  level?: 1 | 2 | 3 | 4 | 5 | 6;
  role?: string;
}

interface SemanticListProps {
  children: React.ReactNode;
  className?: string;
  ordered?: boolean;
  role?: 'list' | 'menu' | 'menubar' | 'tablist';
  'aria-label'?: string;
}

interface SemanticLinkProps {
  href: string;
  children: React.ReactNode;
  className?: string;
  external?: boolean;
  download?: boolean;
  'aria-label'?: string;
  rel?: string;
  title?: string;
}

// Main semantic wrapper
export function SemanticMain({ children, className, id, ...props }: SemanticSectionProps) {
  return (
    <main 
      className={className} 
      id={id || 'main-content'}
      role="main"
      {...props}
    >
      {children}
    </main>
  );
}

// Semantic section component
export function SemanticSection({ children, className, id, ...props }: SemanticSectionProps) {
  return (
    <section 
      className={className} 
      id={id}
      {...props}
    >
      {children}
    </section>
  );
}

// Semantic article component with microdata support
export function SemanticArticle({ 
  children, 
  className, 
  id, 
  itemScope = false,
  itemType,
  ...props 
}: SemanticArticleProps) {
  return (
    <article 
      className={className} 
      id={id}
      itemScope={itemScope}
      itemType={itemType}
      {...props}
    >
      {children}
    </article>
  );
}

// Semantic header component
export function SemanticHeader({ 
  children, 
  className, 
  id, 
  level = 1,
  role,
  ...props 
}: SemanticHeaderProps) {
  const HeaderTag = `h${level}` as keyof React.JSX.IntrinsicElements;
  
  return (
    <HeaderTag 
      className={className} 
      id={id}
      role={role}
      {...props}
    >
      {children}
    </HeaderTag>
  );
}

// Semantic navigation component
export function SemanticNav({ children, className, id, ...props }: SemanticSectionProps) {
  return (
    <nav 
      className={className} 
      id={id}
      role="navigation"
      {...props}
    >
      {children}
    </nav>
  );
}

// Semantic aside component
export function SemanticAside({ children, className, id, ...props }: SemanticSectionProps) {
  return (
    <aside 
      className={className} 
      id={id}
      role="complementary"
      {...props}
    >
      {children}
    </aside>
  );
}

// Semantic footer component
export function SemanticFooter({ children, className, id, ...props }: SemanticSectionProps) {
  return (
    <footer 
      className={className} 
      id={id}
      role="contentinfo"
      {...props}
    >
      {children}
    </footer>
  );
}

// Semantic list component
export function SemanticList({ 
  children, 
  className, 
  ordered = false, 
  role = 'list',
  ...props 
}: SemanticListProps) {
  const ListTag = ordered ? 'ol' : 'ul';
  
  return (
    <ListTag 
      className={className} 
      role={role}
      {...props}
    >
      {children}
    </ListTag>
  );
}

// Semantic list item component
export function SemanticListItem({ 
  children, 
  className, 
  id,
  role = 'listitem'
}: SemanticSectionProps & { role?: string }) {
  return (
    <li 
      className={className} 
      id={id}
      role={role}
    >
      {children}
    </li>
  );
}

// Semantic link component with SEO optimizations
export function SemanticLink({ 
  href, 
  children, 
  className, 
  external = false,
  download = false,
  rel,
  title,
  ...props 
}: SemanticLinkProps) {
  const linkRel = external 
    ? (rel ? `${rel} noopener noreferrer` : 'noopener noreferrer')
    : rel;
  
  const linkProps = {
    href,
    className,
    rel: linkRel,
    title,
    target: external ? '_blank' : undefined,
    download: download ? true : undefined,
    ...props
  };

  return <a {...linkProps}>{children}</a>;
}

// Semantic figure component for images and media
export function SemanticFigure({ 
  children, 
  className, 
  id,
  caption,
  ...props 
}: SemanticSectionProps & { caption?: string }) {
  return (
    <figure 
      className={className} 
      id={id}
      {...props}
    >
      {children}
      {caption && <figcaption>{caption}</figcaption>}
    </figure>
  );
}

// Semantic time component
export function SemanticTime({ 
  children, 
  className, 
  dateTime,
  title
}: {
  children: React.ReactNode;
  className?: string;
  dateTime: string;
  title?: string;
}) {
  return (
    <time 
      className={className} 
      dateTime={dateTime}
      title={title}
    >
      {children}
    </time>
  );
}

// Semantic address component
export function SemanticAddress({ 
  children, 
  className, 
  id 
}: SemanticSectionProps) {
  return (
    <address 
      className={className} 
      id={id}
    >
      {children}
    </address>
  );
}

// Semantic blockquote component
export function SemanticBlockquote({ 
  children, 
  className, 
  id,
  cite,
  author
}: SemanticSectionProps & { cite?: string; author?: string }) {
  return (
    <blockquote 
      className={className} 
      id={id}
      cite={cite}
    >
      {children}
      {author && <cite>— {author}</cite>}
    </blockquote>
  );
}

// Semantic details/summary component for FAQ
export function SemanticDetails({ 
  children, 
  className, 
  id,
  summary,
  open = false
}: SemanticSectionProps & { summary: string; open?: boolean }) {
  return (
    <details 
      className={className} 
      id={id}
      open={open}
    >
      <summary>{summary}</summary>
      {children}
    </details>
  );
}

// Semantic table component
export function SemanticTable({ 
  children, 
  className, 
  id,
  caption,
  ...props 
}: SemanticSectionProps & { caption?: string }) {
  return (
    <table 
      className={className} 
      id={id}
      role="table"
      {...props}
    >
      {caption && <caption>{caption}</caption>}
      {children}
    </table>
  );
}

// Semantic form component
export function SemanticForm({ 
  children, 
  className, 
  id,
  onSubmit,
  method = 'POST',
  action,
  ...props 
}: SemanticSectionProps & {
  onSubmit?: (e: React.FormEvent) => void;
  method?: string;
  action?: string;
}) {
  return (
    <form 
      className={className} 
      id={id}
      onSubmit={onSubmit}
      method={method}
      action={action}
      {...props}
    >
      {children}
    </form>
  );
}

// Semantic fieldset component
export function SemanticFieldset({ 
  children, 
  className, 
  id,
  legend,
  ...props 
}: SemanticSectionProps & { legend?: string }) {
  return (
    <fieldset 
      className={className} 
      id={id}
      {...props}
    >
      {legend && <legend>{legend}</legend>}
      {children}
    </fieldset>
  );
}

// Semantic label component
export function SemanticLabel({ 
  children, 
  className, 
  htmlFor,
  required = false
}: {
  children: React.ReactNode;
  className?: string;
  htmlFor: string;
  required?: boolean;
}) {
  return (
    <label 
      className={className} 
      htmlFor={htmlFor}
    >
      {children}
      {required && <span aria-label="obrigatório"> *</span>}
    </label>
  );
}
