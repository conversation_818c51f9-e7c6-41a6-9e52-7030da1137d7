'use client';

import React, { useState } from 'react';
import { generateAffiliateUrl, trackAffiliateClick, AFFILIATE_LINKS } from '@/lib/affiliate';

interface AffiliateButtonProps {
  affiliateId: string;
  variant?: 'primary' | 'secondary' | 'outline' | 'gradient';
  size?: 'sm' | 'md' | 'lg' | 'xl';
  children: React.ReactNode;
  className?: string;
  additionalParams?: Record<string, string>;
  context?: string;
  fullWidth?: boolean;
  icon?: React.ReactNode;
  loading?: boolean;
  disabled?: boolean;
}

const AffiliateButton: React.FC<AffiliateButtonProps> = ({
  affiliateId,
  variant = 'primary',
  size = 'md',
  children,
  className = '',
  additionalParams,
  context,
  fullWidth = false,
  icon,
  loading = false,
  disabled = false
}) => {
  const [isClicked, setIsClicked] = useState(false);
  
  const affiliate = AFFILIATE_LINKS[affiliateId];
  
  if (!affiliate || !affiliate.isActive) {
    return null;
  }

  const handleClick = () => {
    if (disabled || loading) return;
    
    setIsClicked(true);
    trackAffiliateClick(affiliateId, context);
    
    // Reset clicked state after animation
    setTimeout(() => setIsClicked(false), 200);
    
    // Open affiliate link
    const url = generateAffiliateUrl(affiliateId, additionalParams);
    window.open(url, '_blank', 'noopener,noreferrer');
  };

  // Base styles
  const baseStyles = `
    inline-flex items-center justify-center font-semibold transition-all duration-300 
    transform hover:scale-105 active:scale-95 focus:outline-none focus:ring-4 
    focus:ring-yellow-500/50 disabled:opacity-50 disabled:cursor-not-allowed
    disabled:transform-none relative overflow-hidden
  `;

  // Size styles
  const sizeStyles = {
    sm: 'px-3 py-2 text-sm gap-2',
    md: 'px-6 py-3 text-base gap-2',
    lg: 'px-8 py-4 text-lg gap-3',
    xl: 'px-10 py-5 text-xl gap-3'
  };

  // Variant styles
  const variantStyles = {
    primary: `
      bg-yellow-500 hover:bg-yellow-600 text-black shadow-lg hover:shadow-xl
      border-2 border-yellow-500 hover:border-yellow-600
    `,
    secondary: `
      bg-gray-700 hover:bg-gray-600 text-white shadow-lg hover:shadow-xl
      border-2 border-gray-700 hover:border-gray-600
    `,
    outline: `
      bg-transparent hover:bg-yellow-500 text-yellow-500 hover:text-black
      border-2 border-yellow-500 shadow-lg hover:shadow-xl
    `,
    gradient: `
      bg-gradient-to-r from-yellow-500 to-orange-500 hover:from-yellow-600 
      hover:to-orange-600 text-black shadow-lg hover:shadow-xl border-2 
      border-transparent
    `
  };

  const buttonClasses = `
    ${baseStyles}
    ${sizeStyles[size]}
    ${variantStyles[variant]}
    ${fullWidth ? 'w-full' : ''}
    ${isClicked ? 'animate-pulse' : ''}
    ${className}
  `;

  return (
    <button
      type="button"
      className={buttonClasses}
      onClick={handleClick}
      disabled={disabled || loading}
      aria-label={`Acessar ${affiliate.name}`}
    >
      {/* Loading spinner */}
      {loading && (
        <div className="absolute inset-0 flex items-center justify-center bg-inherit">
          <div className="w-5 h-5 border-2 border-current border-t-transparent rounded-full animate-spin" />
        </div>
      )}
      
      {/* Button content */}
      <div className={`flex items-center gap-2 ${loading ? 'opacity-0' : 'opacity-100'}`}>
        {icon && <span className="flex-shrink-0">{icon}</span>}
        <span>{children}</span>
        
        {/* External link icon */}
        <svg 
          className="w-4 h-4 flex-shrink-0" 
          fill="none" 
          stroke="currentColor" 
          viewBox="0 0 24 24"
        >
          <path 
            strokeLinecap="round" 
            strokeLinejoin="round" 
            strokeWidth={2} 
            d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" 
          />
        </svg>
      </div>

      {/* Ripple effect */}
      {isClicked && (
        <div className="absolute inset-0 bg-white/20 rounded-lg animate-ping" />
      )}
    </button>
  );
};

export default AffiliateButton;
