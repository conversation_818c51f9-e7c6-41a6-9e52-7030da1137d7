'use client';

import React, { useState, useEffect } from 'react';
import AffiliateButton from './AffiliateButton';
import { trackConversion } from '@/lib/affiliate';

interface ConversionBannerProps {
  type?: 'welcome' | 'bonus' | 'urgency' | 'social-proof';
  position?: 'top' | 'bottom' | 'floating';
  dismissible?: boolean;
  autoShow?: boolean;
  delay?: number;
}

const ConversionBanner: React.FC<ConversionBannerProps> = ({
  type = 'welcome',
  position = 'top',
  dismissible = true,
  autoShow = true,
  delay = 3000
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [isDismissed, setIsDismissed] = useState(false);

  useEffect(() => {
    // Check if banner was previously dismissed
    const dismissed = localStorage.getItem(`banner-dismissed-${type}`);
    if (dismissed) {
      setIsDismissed(true);
      return;
    }

    if (autoShow) {
      const timer = setTimeout(() => {
        setIsVisible(true);
        trackConversion({
          type: 'view',
          affiliateId: 'main_casino',
          metadata: { banner_type: type, position }
        });
      }, delay);

      return () => clearTimeout(timer);
    }
  }, [autoShow, delay, type, position]);

  const handleDismiss = () => {
    setIsVisible(false);
    setIsDismissed(true);
    localStorage.setItem(`banner-dismissed-${type}`, 'true');
  };

  if (isDismissed || !isVisible) {
    return null;
  }

  const bannerContent = {
    welcome: {
      title: '🎉 Bem-vindo ao Win222 Brasil!',
      subtitle: 'Ganhe até R$ 5.000 + 200 rodadas grátis no seu primeiro depósito',
      cta: 'Cadastrar Agora',
      affiliateId: 'welcome_bonus',
      bgClass: 'bg-gradient-to-r from-yellow-500 to-orange-500',
      textClass: 'text-black'
    },
    bonus: {
      title: '💰 Oferta Especial Limitada!',
      subtitle: 'Bônus de 100% até R$ 1.000 + giros grátis válido por 24h',
      cta: 'Resgatar Bônus',
      affiliateId: 'welcome_bonus',
      bgClass: 'bg-gradient-to-r from-purple-600 to-pink-600',
      textClass: 'text-white'
    },
    urgency: {
      title: '⏰ Últimas Horas!',
      subtitle: 'Promoção especial termina em breve. Não perca esta oportunidade!',
      cta: 'Aproveitar Agora',
      affiliateId: 'main_casino',
      bgClass: 'bg-gradient-to-r from-red-600 to-red-700',
      textClass: 'text-white'
    },
    'social-proof': {
      title: '🏆 +50.000 jogadores confiam em nós',
      subtitle: 'Junte-se aos vencedores e comece a ganhar hoje mesmo',
      cta: 'Começar a Jogar',
      affiliateId: 'main_casino',
      bgClass: 'bg-gradient-to-r from-green-600 to-blue-600',
      textClass: 'text-white'
    }
  };

  const content = bannerContent[type];

  const positionClasses = {
    top: 'top-0 left-0 right-0',
    bottom: 'bottom-0 left-0 right-0',
    floating: 'bottom-4 left-4 right-4 rounded-lg shadow-2xl'
  };

  return (
    <div
      className={`
        fixed z-50 ${positionClasses[position]} ${content.bgClass} ${content.textClass}
        transform transition-all duration-500 ease-out
        ${isVisible ? 'translate-y-0 opacity-100' : 
          position === 'top' ? '-translate-y-full opacity-0' : 
          position === 'bottom' ? 'translate-y-full opacity-0' : 
          'translate-y-full opacity-0'}
      `}
    >
      <div className="container mx-auto px-4 py-3">
        <div className="flex items-center justify-between gap-4">
          {/* Content */}
          <div className="flex-1 min-w-0">
            <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-4">
              <div className="flex-1 min-w-0">
                <h3 className="font-bold text-sm sm:text-base truncate">
                  {content.title}
                </h3>
                <p className="text-xs sm:text-sm opacity-90 line-clamp-2">
                  {content.subtitle}
                </p>
              </div>
              
              {/* CTA Button */}
              <div className="flex-shrink-0">
                <AffiliateButton
                  affiliateId={content.affiliateId}
                  variant={content.textClass === 'text-black' ? 'secondary' : 'primary'}
                  size="sm"
                  context={`banner-${type}`}
                  additionalParams={{ banner_position: position }}
                >
                  {content.cta}
                </AffiliateButton>
              </div>
            </div>
          </div>

          {/* Dismiss Button */}
          {dismissible && (
            <button
              onClick={handleDismiss}
              className={`
                flex-shrink-0 p-1 rounded-full transition-colors duration-200
                ${content.textClass === 'text-black' 
                  ? 'hover:bg-black/10 text-black/70 hover:text-black' 
                  : 'hover:bg-white/10 text-white/70 hover:text-white'}
              `}
              aria-label="Fechar banner"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          )}
        </div>
      </div>

      {/* Progress bar for urgency banners */}
      {type === 'urgency' && (
        <div className="absolute bottom-0 left-0 right-0 h-1 bg-white/20">
          <div 
            className="h-full bg-white/50 transition-all duration-1000 ease-linear"
            style={{ 
              width: '100%',
              animation: 'countdown 24h linear forwards'
            }}
          />
        </div>
      )}

      <style jsx>{`
        @keyframes countdown {
          from { width: 100%; }
          to { width: 0%; }
        }
      `}</style>
    </div>
  );
};

export default ConversionBanner;
