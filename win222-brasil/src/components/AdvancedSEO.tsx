'use client';

import { Metadata } from 'next';
import Script from 'next/script';

interface AdvancedSEOProps {
  title?: string;
  description?: string;
  keywords?: string;
  canonicalUrl?: string;
  ogImage?: string;
  structuredData?: Record<string, unknown>;
  preloadResources?: Array<{
    href: string;
    as: 'style' | 'script' | 'font' | 'image';
    type?: string;
    crossOrigin?: 'anonymous' | 'use-credentials';
  }>;
  prefetchUrls?: string[];
}

export function generateAdvancedMetadata({
  title = 'Win222 Brasil - Jogos Online Premium',
  description = 'Plataforma líder em jogos online no Brasil com segurança garantida e bônus exclusivos.',
  keywords = 'win222, jogos online, cassino online, apostas brasil',
  canonicalUrl,
  ogImage = '/images/og-image.jpg'
}: Partial<AdvancedSEOProps> = {}): Metadata {
  const baseUrl = 'https://win222brasil.com';
  const fullCanonicalUrl = canonicalUrl ? `${baseUrl}${canonicalUrl}` : baseUrl;
  const fullOgImage = ogImage.startsWith('http') ? ogImage : `${baseUrl}${ogImage}`;

  return {
    title,
    description,
    keywords,
    
    // Canonical URL
    alternates: {
      canonical: fullCanonicalUrl,
    },

    // Open Graph
    openGraph: {
      title,
      description,
      url: fullCanonicalUrl,
      siteName: 'Win222 Brasil',
      images: [
        {
          url: fullOgImage,
          width: 1200,
          height: 630,
          alt: title,
        },
      ],
      locale: 'pt_BR',
      type: 'website',
    },

    // Twitter
    twitter: {
      card: 'summary_large_image',
      title,
      description,
      images: [fullOgImage],
      site: '@win222brasil',
      creator: '@win222brasil',
    },

    // Additional meta tags
    other: {
      'theme-color': '#1a1a1a',
      'msapplication-TileColor': '#1a1a1a',
      'apple-mobile-web-app-capable': 'yes',
      'apple-mobile-web-app-status-bar-style': 'black-translucent',
      'format-detection': 'telephone=no',
      'mobile-web-app-capable': 'yes',
    },

    // Robots
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },
  };
}

export default function AdvancedSEO({
  preloadResources = [],
  prefetchUrls = [],
  structuredData
}: AdvancedSEOProps) {
  return (
    <>
      {/* Preload critical resources */}
      {preloadResources.map((resource, index) => (
        <link
          key={index}
          rel="preload"
          href={resource.href}
          as={resource.as}
          type={resource.type}
          crossOrigin={resource.crossOrigin}
        />
      ))}

      {/* Prefetch important pages */}
      {prefetchUrls.map((url, index) => (
        <link key={index} rel="prefetch" href={url} />
      ))}

      {/* DNS prefetch for external resources */}
      <link rel="dns-prefetch" href="//www.google-analytics.com" />
      <link rel="dns-prefetch" href="//www.googletagmanager.com" />
      <link rel="dns-prefetch" href="//fonts.googleapis.com" />
      <link rel="dns-prefetch" href="//fonts.gstatic.com" />
      <link rel="dns-prefetch" href="//connect.facebook.net" />

      {/* Preconnect to critical third-party origins */}
      <link rel="preconnect" href="https://fonts.googleapis.com" />
      <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
      <link rel="preconnect" href="https://www.google-analytics.com" />

      {/* Structured Data */}
      {structuredData && (
        <Script
          id="structured-data"
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(structuredData)
          }}
        />
      )}

      {/* Critical CSS for above-the-fold content */}
      <style dangerouslySetInnerHTML={{
        __html: `
          /* Critical CSS for immediate rendering */
          .hero-section {
            background: linear-gradient(135deg, #1a1a1a 0%, #2d3748 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
          }

          .critical-text {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            color: white;
            text-align: center;
          }

          .cta-button {
            background: linear-gradient(45deg, #f59e0b, #d97706);
            color: white;
            padding: 12px 24px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 600;
            display: inline-block;
            margin-top: 16px;
          }
        `
      }} />

      {/* Performance monitoring */}
      <Script id="performance-monitor" strategy="afterInteractive">
        {`
          // Monitor Core Web Vitals
          function sendToAnalytics(metric) {
            if (typeof gtag !== 'undefined') {
              gtag('event', metric.name, {
                event_category: 'Web Vitals',
                event_label: metric.id,
                value: Math.round(metric.name === 'CLS' ? metric.value * 1000 : metric.value),
                non_interaction: true,
              });
            }
          }

          // Import web-vitals library dynamically
          import('web-vitals').then(({ onCLS, onINP, onFCP, onLCP, onTTFB }) => {
            onCLS(sendToAnalytics);
            onINP(sendToAnalytics);
            onFCP(sendToAnalytics);
            onLCP(sendToAnalytics);
            onTTFB(sendToAnalytics);
          });

          // Resource timing monitoring
          if ('PerformanceObserver' in window) {
            const observer = new PerformanceObserver((list) => {
              list.getEntries().forEach((entry) => {
                if (entry.entryType === 'largest-contentful-paint') {
                  console.log('LCP:', entry.startTime);
                }
                if (entry.entryType === 'first-input') {
                  console.log('FID:', entry.processingStart - entry.startTime);
                }
              });
            });
            
            observer.observe({ entryTypes: ['largest-contentful-paint', 'first-input'] });
          }
        `}
      </Script>

      {/* Service Worker registration for caching */}
      <Script id="sw-registration" strategy="afterInteractive">
        {`
          if ('serviceWorker' in navigator) {
            window.addEventListener('load', function() {
              navigator.serviceWorker.register('/sw.js')
                .then(function(registration) {
                  console.log('SW registered: ', registration);
                })
                .catch(function(registrationError) {
                  console.log('SW registration failed: ', registrationError);
                });
            });
          }
        `}
      </Script>

      {/* Image optimization hints */}
      <Script id="image-optimization" strategy="afterInteractive">
        {`
          // Lazy load images with Intersection Observer
          if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver((entries, observer) => {
              entries.forEach(entry => {
                if (entry.isIntersecting) {
                  const img = entry.target;
                  img.src = img.dataset.src;
                  img.classList.remove('lazy');
                  img.classList.add('loaded');
                  observer.unobserve(img);
                }
              });
            });

            document.querySelectorAll('img[data-src]').forEach(img => {
              imageObserver.observe(img);
            });
          }

          // Preload critical images
          const criticalImages = [
            '/images/hero-bg.webp',
            '/images/logo.webp',
            '/images/featured-games.webp'
          ];

          criticalImages.forEach(src => {
            const link = document.createElement('link');
            link.rel = 'preload';
            link.as = 'image';
            link.href = src;
            document.head.appendChild(link);
          });
        `}
      </Script>

      {/* Font optimization */}
      <Script id="font-optimization" strategy="afterInteractive">
        {`
          // Font display optimization
          if ('fonts' in document) {
            document.fonts.ready.then(() => {
              document.body.classList.add('fonts-loaded');
            });
          }
        `}
      </Script>
    </>
  );
}

// Hook for getting user agent info for optimization
export function useUserAgent() {
  if (typeof window === 'undefined') {
    return null;
  }

  const userAgent = navigator.userAgent;
  const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent);
  const isTablet = /iPad|Android(?!.*Mobile)/i.test(userAgent);
  const isDesktop = !isMobile && !isTablet;
  const isChrome = /Chrome/i.test(userAgent);
  const isSafari = /Safari/i.test(userAgent) && !/Chrome/i.test(userAgent);
  const isFirefox = /Firefox/i.test(userAgent);

  return {
    isMobile,
    isTablet,
    isDesktop,
    isChrome,
    isSafari,
    isFirefox,
    userAgent
  };
}



// Component for prefetching next likely pages
export function SmartPrefetch({ currentPage }: { currentPage: string }) {
  const prefetchMap: Record<string, string[]> = {
    '/': ['/jogos', '/cassino', '/sobre'],
    '/jogos': ['/cassino', '/guias', '/faq'],
    '/cassino': ['/jogos', '/promocoes', '/sobre'],
    '/guias': ['/faq', '/glossario', '/jogos'],
    '/faq': ['/suporte', '/sobre', '/guias'],
    '/glossario': ['/guias', '/faq', '/jogos'],
    '/cidades': ['/sobre', '/faq', '/jogos']
  };

  const urlsToPrefetch = prefetchMap[currentPage] || [];

  return (
    <>
      {urlsToPrefetch.map((url, index) => (
        <link key={index} rel="prefetch" href={url} />
      ))}
    </>
  );
}
