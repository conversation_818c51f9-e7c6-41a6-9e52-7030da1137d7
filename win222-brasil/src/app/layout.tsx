import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON>, <PERSON>o } from "next/font/google";
import "./globals.css";
import { seoConfig } from "@/config/seo";
import { WebsiteSchema, OrganizationSchema, LocalBusinessSchema, SoftwareApplicationSchema } from "@/components/StructuredData";
// import WebVitals from "@/components/WebVitals";
// import PerformanceOptimizer from "@/components/PerformanceOptimizer";
// import AdvancedSEO from "@/components/AdvancedSEO";
import { CriticalResourceHints } from "@/components/CriticalResourceHints";
import { ABTestProvider } from "@/components/ABTestProvider";

// 优化字体选择 - 更适合葡萄牙语
const inter = Inter({
  variable: "--font-inter",
  subsets: ["latin"],
  display: "swap",
});

const roboto = Roboto({
  variable: "--font-roboto",
  weight: ["300", "400", "500", "700"],
  subsets: ["latin"],
  display: "swap",
});

export const metadata: Metadata = {
  title: {
    default: seoConfig.defaultTitle,
    template: seoConfig.titleTemplate,
  },
  description: seoConfig.defaultDescription,
  keywords: [
    "win222",
    "win222 brasil",
    "jogos online",
    "cassino online",
    "apostas online",
    "entretenimento digital",
    "jogos brasil",
    "plataforma jogos",
  ],
  authors: [{ name: "Win222 Brasil" }],
  creator: "Win222 Brasil",
  publisher: "Win222 Brasil",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL(seoConfig.siteUrl),
  alternates: {
    canonical: "/",
    languages: {
      "pt-BR": "/",
    },
  },
  openGraph: {
    type: "website",
    locale: "pt_BR",
    url: seoConfig.siteUrl,
    siteName: seoConfig.siteName,
    title: seoConfig.openGraph.title,
    description: seoConfig.openGraph.description,
    images: seoConfig.openGraph.images,
  },
  twitter: {
    card: "summary_large_image",
    site: seoConfig.twitter.site,
    creator: seoConfig.twitter.handle,
    title: seoConfig.defaultTitle,
    description: seoConfig.defaultDescription,
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
  verification: {
    google: "google-site-verification-code",
    yandex: "yandex-verification-code",
    yahoo: "yahoo-site-verification-code",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="pt-BR">
      <head>
        <link rel="icon" href="/favicon.ico" sizes="any" />
        <link rel="icon" href="/icon.svg" type="image/svg+xml" />
        <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
        <link rel="manifest" href="/manifest.json" />
        <meta name="theme-color" content="#1a1a1a" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />

        {/* Critical Resource Hints */}
        <CriticalResourceHints />

        {/* Preconnect para performance */}
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />

        {/* JSON-LD estruturado */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(seoConfig.jsonLd),
          }}
        />
      </head>
      <body
        className={`${inter.variable} ${roboto.variable} font-inter antialiased`}
      >
        <ABTestProvider>
          {children}
        </ABTestProvider>

        {/* Componentes de SEO e Performance */}
        <WebsiteSchema />
        <OrganizationSchema />
        <LocalBusinessSchema />
        <SoftwareApplicationSchema />
        {/* <WebVitals debug={process.env.NODE_ENV === 'development'} /> */}
        {/* Performance components temporarily disabled for build */}
        {/* <PerformanceOptimizer
          enableResourceHints={true}
          enableImageOptimization={true}
          enableFontOptimization={true}
          enableCriticalCSS={true}
          debug={process.env.NODE_ENV === 'development'}
        />
        <AdvancedSEO
          preloadResources={[
            { href: '/fonts/inter-var.woff2', as: 'font', type: 'font/woff2', crossOrigin: 'anonymous' },
            { href: '/styles/critical.css', as: 'style' }
          ]}
          prefetchUrls={['/jogos', '/cassino', '/sobre']}
        /> */}

        {/* Google Analytics será adicionado via next/script quando configurado */}
      </body>
    </html>
  );
}
