import { MetadataRoute } from 'next'
// import { seoConfig, urlStructure } from '@/config/seo'

export default function sitemap(): MetadataRoute.Sitemap {
  const baseUrl = 'https://win222brasil.com'
  
  // Static pages with high priority
  const staticPages = [
    {
      url: baseUrl,
      lastModified: new Date(),
      changeFrequency: 'daily' as const,
      priority: 1,
    },
    {
      url: `${baseUrl}/jogos`,
      lastModified: new Date(),
      changeFrequency: 'daily' as const,
      priority: 0.9,
    },
    {
      url: `${baseUrl}/cassino`,
      lastModified: new Date(),
      changeFrequency: 'daily' as const,
      priority: 0.9,
    },
  ]

  // Game category pages
  const gameCategories = [
    '/jogos/slots',
    '/jogos/poker',
    '/jogos/blackjack',
    '/jogos/roleta',
    '/jogos/baccarat',
    '/jogos/ao-vivo',
    '/cassino/slots',
    '/cassino/mesa',
    '/cassino/ao-vivo',
    '/apostas/futebol',
    '/apostas/basquete',
    '/apostas/tenis',
    '/apostas/esports',
  ].map(path => ({
    url: `${baseUrl}${path}`,
    lastModified: new Date(),
    changeFrequency: 'weekly' as const,
    priority: 0.8,
  }))

  // Popular game pages
  const popularGames = [
    '/jogos/fortune-tiger',
    '/jogos/aviator',
    '/jogos/mines',
    '/jogos/spaceman',
    '/jogos/sweet-bonanza',
    '/jogos/gates-of-olympus',
    '/cassino/lightning-roulette',
    '/cassino/crazy-time',
    '/cassino/monopoly-live',
  ].map(path => ({
    url: `${baseUrl}${path}`,
    lastModified: new Date(),
    changeFrequency: 'weekly' as const,
    priority: 0.7,
  }))

  // Promotional pages
  const promotionalPages = [
    '/promocoes/bonus-boas-vindas',
    '/promocoes/rodadas-gratis',
    '/promocoes/cashback',
    '/promocoes/programa-vip',
    '/promocoes/torneios',
  ].map(path => ({
    url: `${baseUrl}${path}`,
    lastModified: new Date(),
    changeFrequency: 'weekly' as const,
    priority: 0.7,
  }))

  // Blog/Guide pages for SEO
  const guidePagesForSEO = [
    '/guias',
    '/guias/como-jogar-slots',
    '/guias/estrategias-poker',
    '/guias/regras-blackjack',
    '/guias/dicas-apostas-futebol',
    '/guias/jogo-responsavel',
    '/blog/melhores-slots-2024',
    '/blog/como-ganhar-no-cassino',
    '/blog/apostas-esportivas-brasil',
  ].map(path => ({
    url: `${baseUrl}${path}`,
    lastModified: new Date(),
    changeFrequency: 'monthly' as const,
    priority: 0.6,
  }))

  // Long-tail keyword pages
  const longTailPages = [
    '/faq',
    '/glossario',
    '/cidades',
  ].map(path => ({
    url: `${baseUrl}${path}`,
    lastModified: new Date(),
    changeFrequency: 'weekly' as const,
    priority: 0.7,
  }))

  return staticPages;
}
