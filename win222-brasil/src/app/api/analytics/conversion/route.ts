import { NextRequest, NextResponse } from 'next/server';

interface ConversionData {
  type: 'click' | 'signup' | 'deposit' | 'play' | 'view';
  affiliateId: string;
  userId?: string;
  value?: number;
  metadata?: Record<string, any>;
  timestamp: string;
  userAgent: string;
  referrer: string;
  url: string;
}

export async function POST(request: NextRequest) {
  try {
    const data: ConversionData = await request.json();
    
    // Validate required fields
    if (!data.type || !data.affiliateId || !data.timestamp) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Get client IP
    const clientIP = request.headers.get('x-forwarded-for') || 
                    request.headers.get('x-real-ip') || 
                    'unknown';

    // Enhanced conversion data
    const conversionEvent = {
      ...data,
      ip: clientIP,
      country: request.geo?.country || 'unknown',
      city: request.geo?.city || 'unknown',
      region: request.geo?.region || 'unknown',
      processed_at: new Date().toISOString(),
    };

    // Log conversion for analytics (in production, send to analytics service)
    console.log('Conversion Event:', JSON.stringify(conversionEvent, null, 2));

    // In production, you would:
    // 1. Store in database
    // 2. Send to analytics platforms (Google Analytics, Facebook Pixel, etc.)
    // 3. Trigger webhooks to affiliate networks
    // 4. Update conversion tracking systems

    // Example: Send to Google Analytics 4
    if (process.env.GA_MEASUREMENT_ID && process.env.GA_API_SECRET) {
      try {
        const gaResponse = await fetch(
          `https://www.google-analytics.com/mp/collect?measurement_id=${process.env.GA_MEASUREMENT_ID}&api_secret=${process.env.GA_API_SECRET}`,
          {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              client_id: data.userId || 'anonymous',
              events: [
                {
                  name: 'affiliate_conversion',
                  parameters: {
                    event_category: 'affiliate',
                    event_label: data.affiliateId,
                    value: data.value || 0,
                    conversion_type: data.type,
                    affiliate_id: data.affiliateId,
                    custom_parameters: data.metadata,
                  },
                },
              ],
            }),
          }
        );

        if (!gaResponse.ok) {
          console.error('Failed to send to Google Analytics:', await gaResponse.text());
        }
      } catch (error) {
        console.error('Error sending to Google Analytics:', error);
      }
    }

    // Example: Send to Facebook Pixel
    if (process.env.FACEBOOK_PIXEL_ID && process.env.FACEBOOK_ACCESS_TOKEN) {
      try {
        const fbResponse = await fetch(
          `https://graph.facebook.com/v18.0/${process.env.FACEBOOK_PIXEL_ID}/events`,
          {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              data: [
                {
                  event_name: 'Purchase', // or other standard events
                  event_time: Math.floor(new Date(data.timestamp).getTime() / 1000),
                  action_source: 'website',
                  user_data: {
                    client_ip_address: clientIP,
                    client_user_agent: data.userAgent,
                  },
                  custom_data: {
                    currency: 'BRL',
                    value: data.value || 0,
                    content_type: 'affiliate_conversion',
                    content_category: data.affiliateId,
                  },
                },
              ],
              access_token: process.env.FACEBOOK_ACCESS_TOKEN,
            }),
          }
        );

        if (!fbResponse.ok) {
          console.error('Failed to send to Facebook Pixel:', await fbResponse.text());
        }
      } catch (error) {
        console.error('Error sending to Facebook Pixel:', error);
      }
    }

    // Return success response
    return NextResponse.json({
      success: true,
      message: 'Conversion tracked successfully',
      event_id: `${data.affiliateId}_${Date.now()}`,
    });

  } catch (error) {
    console.error('Error processing conversion:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Handle OPTIONS for CORS
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  });
}
